import { lazy } from 'react';
import { NotFound } from 'common/components';
import { ROUTE_URL } from 'common';
import { routes as sample } from 'pages/others/routes';
import { routes as scholarRoutes } from 'pages/scholar/routes';
import { routes as applicantRoutes } from 'pages/applicant/routes';
import { routes as authRoutes } from 'pages/auth/routes';

const App = lazy(() => import('../App'));
const Layout = lazy(() => import('../layout/Layout'));
const AuthLayout = lazy(() => import('../layout/AuthLayout'));

export const routes = [
  {
    path: ROUTE_URL.ROOT,
    element: <App />,
    errorElement: <NotFound />,
    children: [
      {
        path: ROUTE_URL.BASE_PATH,
        index: true,
        element: <NotFound />
      },
      {
        path: ROUTE_URL.BASE_PATH,
        element: <Layout />,
        errorElement: <NotFound />,
        children: [
          ...sample, ...applicantRoutes, ...scholarRoutes]
      }
    ]
  },
  {
    path: 'ui/auth',
    element: <AuthLayout />,
    errorElement: <NotFound />,
    children: [...authRoutes]
  },
  {
    path: ROUTE_URL.NOT_FOUND,
    element: <NotFound />
  }
];
