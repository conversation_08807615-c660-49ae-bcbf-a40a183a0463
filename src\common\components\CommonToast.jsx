import { useEffect } from 'react';
import { useToast } from 'common/components';
import { useSelector, useDispatch } from 'react-redux';
import { actions as commonActions } from 'pages/common/slice';
import { getCustomToast } from 'pages/common/selectors';
import { TOST_CONFIG } from 'common/constants';

const CommonToast = () => {
  const toast = useToast();
  const dispatch = useDispatch();
  const toastAction = useSelector(getCustomToast);

  const {
    open = false,
    variant = 'info',
    message = '',
    title = 'Info'
  } = toastAction;

  useEffect(() => {
    if (open) {
      toast({
        ...TOST_CONFIG,
        title,
        position: 'top-right',
        description: message,
        status: variant
      });

      dispatch(commonActions.setCustomToast({
        open: false, variant: 'info', message: '', title: ''
      }));
    }
  }, [open, toast, message, dispatch, variant]);

  return null;
};

export default CommonToast;
