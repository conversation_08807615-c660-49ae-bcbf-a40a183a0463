export const STATE_REDUCER_KEY = {
  SLICE: 'file-download',
  API: 'file-download-api'
};

export const RESPONSE_TYPE = {
  FILE_STREAM: 'FILE_STREAM',
  FILE_BYTE: 'FILE_BYTE',
  BLOB: 'BLOB',
  ARRAY_BUFFER: 'ARRAY_BUFFER',
  TEXT: 'TEXT',
  JSON: 'JSON'
};

export const FILE_HEX_SIGNATURE = {
  '1A45DFA3': { type: 'video/webm', ext: 'webm' },
  66747970: { type: 'video/mp4', ext: 'mp4' },
  '000001BA': { type: 'video/mpeg', ext: 'mpeg' },
  '000001B3': { type: 'video/mpeg', ext: 'mpeg' },
  FFD8FFE0: { type: 'image/jpg', ext: 'jpg' },
  FFD8FFDB: { type: 'image/jpeg', ext: 'jpeg' },
  FFD8FFEE: { type: 'image/jpeg', ext: 'jpeg' },
  FFD8FFE1: { type: 'image/jpeg', ext: 'jpeg' },
  47494638: { type: 'image/gif', ext: 'gif' },
  '89504e47': { type: 'image/png', ext: 'png' },
  25504446: { type: 'application/pdf', ext: 'pdf' }
};
