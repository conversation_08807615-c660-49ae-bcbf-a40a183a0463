import { DATE_FORMAT } from 'common/constants';
import dayjs from 'dayjs';

export const getDayTimePeriod = () => {
  const now = new Date();
  const currentHour = now.getHours();

  if (currentHour >= 5 && currentHour < 12) {
    return 'Good Morning';
  }
  if (currentHour >= 12 && currentHour < 17) {
    return 'Good Afternoon';
  }
  return 'Good Evening';
};

export const formatDateToDDMMYYYY = (data) => {
  const date = new Date(data);
  if (Number.isNaN(date.getTime())) return '';

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${year}-${month}-${day}`;
};
export const formatDateToMMYYYY = (data) => {
  const date = new Date(data);
  if (Number.isNaN(date.getTime())) return '';

  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${month}-${year}`;
};

export const formatDateToYYYYMM = (data) => {
  const date = new Date(data);
  if (Number.isNaN(date.getTime())) return '';

  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${year}-${month}`;
};

export function formatDate(dateStr) {
  const [year, month, day] = dateStr.split('-');
  return `${day}.${month}.${year}`;
}

export function convertToDDMMYYYY(dateString) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}

export const convertDDMMYYYYToDate = (dateString) => {
  if (!dateString) return null;

  const [day, month, year] = dateString.split('-');
  const formattedDate = new Date(`${year}-${month}-${day}T00:00:00`);

  return formattedDate;
};

export const formatDateToMM = (data) => {
  const date = new Date(data);
  if (Number.isNaN(date.getTime())) return '';

  const month = String(date.getMonth() + 1).padStart(2, '0');

  return month;
};

export const formatYYYYMMDDtoDDMMYYYY = (dateString) => {
  if (!dateString || typeof dateString !== 'string') return '';
  const [year, month, day] = dateString.split('-');
  return `${day}-${month}-${year}`;
};

export const formatDateWithDayjs = (date, format = DATE_FORMAT.DATE_YYYYMMDD) => {
  const parsedDate = dayjs(date, {
    format: DATE_FORMAT.DATE_TIME_GMT
  });
  const formattedDate = parsedDate.format(format);
  return formattedDate;
};
