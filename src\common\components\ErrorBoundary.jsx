import React from 'react';
import { ErrorBoundary as ReactErrorBoundary } from 'react-error-boundary';

const fallbackRender = ({ error }, parentPath) => {
  return (
    <div style={{ padding: '1rem', backgroundColor: '#ffe5e5', color: '#d8000c' }}>
      <h2>Something went wrong{parentPath ? ` in ${parentPath}` : ''}:</h2>
      <pre>{error?.message}</pre>
      <pre>{error?.stack}</pre>
    </div>
  );
};

const ErrorBoundary = ({ children, parentPath }) => {
  return (
    <ReactErrorBoundary fallbackRender={(data) => fallbackRender(data, parentPath)}>
      {children}
    </ReactErrorBoundary>
  );
};

export default ErrorBoundary;
