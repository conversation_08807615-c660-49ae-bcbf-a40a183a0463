import * as yup from 'yup';
import { t } from 'common/components';

export const academicDetailsSchema = yup.object().shape({
  // Class 10 Board Exam Details
  board: yup
    .string()
    .required(t('boardRequired')),
  gradePercentage: yup
    .string()
    .required(t('gradeRequired'))
    .matches(/^[0-9.]+$/, t('onlyNumbersAndDecimalAllowed'))
    .test(
      'is-valid-percentage',
      t('validPercentageRequired'),
      (value) => parseFloat(value) >= 0 && parseFloat(value) <= 100
    ),
  institutionName: yup
    .string()
    .required(t('institutionNameRequired'))
    .min(3, t('min3Characters'))
    .max(100, t('max100Characters')),
  institutionType: yup
    .string()
    .required(t('institutionTypeRequired')),
  institutionLocation: yup
    .string()
    .required(t('institutionLocationRequired'))
    .min(3, t('min3Characters'))
    .max(100, t('max100Characters')),
  yearOfCompletion: yup
    .string()
    .required(t('yearOfCompletionRequired')),
  stateOfInstitution: yup
    .string()
    .required(t('stateOfInstitutionRequired')),
  districtOfInstitution: yup
    .string()
    .required(t('districtOfInstitutionRequired')),

  // Current Course Details
  currentInstitutionType: yup
    .string()
    .required(t('currentInstitutionTypeRequired')),
  currentInstitutionName: yup
    .string()
    .required(t('currentInstitutionNameRequired'))
    .min(3, t('min3Characters'))
    .max(100, t('max100Characters')),
  courseMode: yup
    .string()
    .required(t('courseModeRequired')),
  academicYear: yup
    .string()
    .required(t('academicYearRequired')),
  dateOfAdmission: yup
    .date()
    .transform((value, originalValue) => (originalValue === '' ? undefined : value))
    .required(t('dateOfAdmissionRequired'))
    .max(new Date(), t('dateCannotBeFuture'))
});
