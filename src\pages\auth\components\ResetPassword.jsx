import {
  <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ta<PERSON>, Text, VStack, IconButton, Heading,
  useToast
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { BackForward } from 'assets/svg';

import { EMAIL, MOBILE } from 'common/regex';
import { useForgotPasswordMutation } from '../api';

const ResetPassword = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    setError
  } = useForm({
    defaultValues: {
      emailOrPhone: ''
    }
  });

  const toast = useToast();
  const navigate = useNavigate();
  const [forgotPassword] = useForgotPasswordMutation();

  const onSubmit = async (data) => {
    try {
      const isEmail = EMAIL.test(data.emailOrPhone);
      const payload = isEmail
        ? { email: data.emailOrPhone }
        : { mobileNumber: data.emailOrPhone };

      const response = await forgotPassword(payload).unwrap();

      toast({
        title: 'Reset Link Sent',
        description: response.message || 'Please check your email/mobile for the password reset link',
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });

      // Navigate to OTP verification if using that flow
      navigate('/verify-otp', { state: { contact: data.emailOrPhone } });
    } catch (error) {
      if (error.data?.field === 'emailOrPhone') {
        setError('emailOrPhone', {
          type: 'manual',
          message: error.data.message
        });
      } else {
        toast({
          title: 'Error',
          description: error.data?.message || 'Failed to send reset link',
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top-right'
        });
      }
    }
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      minH={{ md: '580px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full" h="full" justify="space-between">
        <VStack w="full" spacing={8}>
          <HStack mb={{ base: 4, md: 6 }} spacing={3} align="flex-start">
            <IconButton
              icon={<BackForward />}
              variant="ghost"
              size="sm"
              aria-label="Go back"
              display={{ base: 'none', md: 'flex' }}
              onClick={() => navigate(-1)}
            />
            <VStack align="start" spacing={0}>
              <Heading fontSize="3xl" fontWeight="bold" color="gray.700">
                Reset Password
              </Heading>
              <Text fontSize="md" color="gray.500">
                Enter your email/mobile to receive a reset link
              </Text>
            </VStack>
          </HStack>

          <VStack spacing={6} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
            <FormController
              type="text"
              name="emailOrPhone"
              control={control}
              label="Email Address / Mobile number"
              placeholder="Enter your email or mobile number"
              rules={{
                required: 'Email or mobile number is required',
                validate: (value) => {
                  const isEmail = EMAIL.test(value);
                  const isPhone = MOBILE.test(value);
                  return isEmail || isPhone || 'Please enter a valid email or 10-digit mobile number';
                }
              }}
              errors={errors}
              inputHeight="50px"
            />
          </VStack>
        </VStack>

        <VStack w="full" spacing={6}>
          <Button
            type="submit"
            variant="secondary"
            w="full"
            isLoading={isSubmitting}
            loadingText="Sending..."
            onClick={handleSubmit(onSubmit)}
          >
            Next
          </Button>

          <Text fontSize="md" color="gray.500" textAlign="center">
            Remember your password?{' '}
            <Link
              to="/login"
              style={{
                color: 'primary.A100',
                textDecoration: 'underline',
                fontWeight: '500'
              }}
            >
              Sign In
            </Link>
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ResetPassword;
