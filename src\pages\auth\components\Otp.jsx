import {
  <PERSON>, <PERSON><PERSON>, Card, <PERSON>Stack, Text, VStack, IconButton, Heading,
  useToast
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { BackForward } from 'assets/svg';
import { useResendOTPMutation, useVerifyOTPMutation } from '../api';

const OTPVerificationPage = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      otp: ''
    }
  });
  const location = useLocation();
  const navigate = useNavigate();
  const { id, mobileNumber, emailId } = location.state || {};

  const toast = useToast();
  const [verifyOTP] = useVerifyOTPMutation();
  const [resendOTP] = useResendOTPMutation();

  const onSubmit = async (data) => {
    try {
      await verifyOTP({
        id,
        otp: data.otp,
        mobileNumber
      }).unwrap();
      toast({
        title: 'Verification Successful',
        description: 'Your account has been verified successfully',
        status: 'success',
        duration: 3000,
        isClosable: true
      });

      navigate('/ui/auth/login');
    } catch (error) {
      toast({
        title: 'Verification Failed',
        description: error.data?.message || 'Invalid OTP. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    }
  };

  const handleResendOTP = async () => {
    try {
      if (!mobileNumber && !emailId) {
        throw new Error('Contact information not available');
      }

      await resendOTP(mobileNumber).unwrap();

      toast({
        title: 'OTP Resent',
        description: `A new OTP has been sent to ${mobileNumber || emailId}`,
        status: 'info',
        duration: 3000,
        isClosable: true
      });
    } catch (error) {
      toast({
        title: 'Failed to Resend OTP',
        description: error.message || error.data?.message || 'Failed to resend OTP. Please try again.',
        status: 'error',
        duration: 3000,
        isClosable: true
      });
    }
  };

  const renderOTPMessage = () => {
    if (mobileNumber) {
      return `We've sent a 6-digit verification code to your mobile number ending with ${mobileNumber.slice(-3)}`;
    }
    if (emailId) {
      return `We've sent a 6-digit verification code to your email ${emailId}`;
    }
    return 'We\'ve sent a 6-digit verification code to your registered contact';
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      display={{ base: 'block', md: 'grid' }}
      minH={{ md: '580px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full">
        <HStack mb={2} spacing={3} mt={5}>
          <IconButton
            icon={<BackForward />}
            variant="ghost"
            size="sm"
            aria-label="Go back"
            display={{ base: 'none', md: 'flex' }}
          />
          <VStack align="start" spacing={0}>
            <Heading fontSize={{ base: '18px', md: '20px' }} color="blue.600">
              Create Your Account
            </Heading>
            <Text fontSize="xs" color="gray.600">
              To complete your registration, please fill in all the fields below
            </Text>
          </VStack>
        </HStack>

        <VStack spacing={6} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
          <Text fontSize="md" color="gray.500" textAlign="center">
            {renderOTPMessage()}
          </Text>

          <FormController
            type="otp"
            name="otp"
            control={control}
            errors={errors}
            rules={{
              required: 'OTP is required',
              minLength: {
                value: 6,
                message: 'OTP must be 6 digits'
              },
              maxLength: {
                value: 6,
                message: 'OTP must be 6 digits'
              }
            }}
            otpProps={{
              placeholder: 'Enter verification code',
              maxLength: 6
            }}
          />

          <Button
            variant="link"
            color="primary.A100"
            fontSize="md"
            onClick={handleResendOTP}
            _hover={{ textDecoration: 'underline' }}
            disabled={isSubmitting}
          >
            Resend OTP
          </Button>

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 12 }}
            isLoading={isSubmitting}
            loadingText="Verifying..."
          >
            Verify
          </Button>

          <Text fontSize="md" color="gray.500" textAlign="center" mt={6}>
            Already registered?{' '}
            <Link to="/ui/login" style={{ color: 'primary.A100', textDecoration: 'underline' }}>
              Sign In
            </Link>
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
};

export default OTPVerificationPage;
