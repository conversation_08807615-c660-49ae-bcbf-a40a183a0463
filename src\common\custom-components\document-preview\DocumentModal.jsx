import React, { useRef, useState, useEffect } from 'react';
import {
  Flex, IconButton, Modal, ModalContent, ModalOverlay, Text, ModalBody, Box
} from 'common/components';
import {
  CloseIcon, DownloadIcon, PrintIcon, RotateIcon, ZoomInIcon, ZoomOutIcon
} from 'assets/svg';
import DocumentView from './DocumentView';

function DocumentModal({
  isOpen = false, onOpen, onClose, fileData = {}, enableToolbar = true
}) {
  const containerRef = useRef(null);
  const [rotate, setRotate] = useState(0);
  const [scale, setScale] = useState(1);
  const [, setBaseScale] = useState(1);

  const { url = '', name = 'Document Preview' } = fileData;
  const PDF_ORIGINAL_WIDTH = 612;

  const handleClose = () => {
    onClose();
    onOpen(false);
    setScale(1);
    setRotate(0);
  };

  const calculateBaseScale = () => {
    if (containerRef.current) {
      const width = containerRef.current.offsetWidth;
      const newBaseScale = width / PDF_ORIGINAL_WIDTH;
      setBaseScale(newBaseScale);
      setScale(newBaseScale);
    }
  };

  useEffect(() => {
    if (isOpen) {
      calculateBaseScale();
      window.addEventListener('resize', calculateBaseScale);
    }
    return () => {
      setScale(1);
      setRotate(0);
      window.removeEventListener('resize', calculateBaseScale);
    };
  }, [isOpen]);

  const handleZoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3));
  const handleZoomOut = () => setScale((prev) => Math.max(prev - 0.2, 0.5));

  const handlePrint = (src) => {
    if (src) {
      const iframe = document.createElement('iframe');
      document.body.appendChild(iframe);
      iframe.style.display = 'none';
      iframe.src = src;
      iframe.onload = () => {
        setTimeout(() => {
          iframe.focus();
          iframe.contentWindow.print();
        }, 1);
      };
    }
  };

  const handleDownload = (src) => {
    if (src) {
      const link = document.createElement('a');
      link.href = src;
      link.download = src.split('/').pop();
      link.setAttribute('download', name);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleRotate = (deg) => {
    if (deg < 270) setRotate(Number(deg) + 90);
    else setRotate(0);
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="5xl" isCentered>
      <ModalOverlay />
      <ModalContent overflow="hidden" maxH="90vh" flexDirection="column" display="flex">
        <Flex
          bg="primary.500"
          color="white"
          p={2}
          minH="60px"
          align="center"
          justifyContent="space-between"
          gap={4}
        >
          {/* LEFT: File name or back button */}
          <Flex align="center" minW={0} flex="1">
            <Text
              fontWeight="semibold"
              fontSize="sm"
              ml={2}
              isTruncated
              title={name}
            >
              {name}
            </Text>
          </Flex>

          {/* CENTER: Zoom controls */}
          {enableToolbar && (
          <Flex align="center" gap={2} justify="center" flex="1" minW={0}>
            <IconButton
              icon={<ZoomOutIcon />}
              aria-label="Zoom Out"
              size="sm"
              _hover={{ bg: 'primary.400' }}
              variant="ghost"
              color="white"
              onClick={() => handleZoomOut()}
            />
            <Text
              fontSize="xs"
              fontWeight="bold"
              bg="primary.100"
              color="black"
              px={1}
              borderRadius="sm"
              minW="48px"
              textAlign="center"
            >
              {(scale * 100).toFixed(0)}%
            </Text>
            <IconButton
              icon={<ZoomInIcon />}
              aria-label="Zoom In"
              size="sm"
              _hover={{ bg: 'primary.400' }}
              variant="ghost"
              color="white"
              onClick={() => handleZoomIn()}
            />
            <IconButton
              icon={<RotateIcon style={{ transform: `rotate(${rotate}deg)` }} />}
              aria-label="Rotate"
              size="sm"
              _hover={{ bg: 'primary.400' }}
              variant="ghost"
              color="white"
              onClick={() => handleRotate(rotate)}
            />
          </Flex>
          )}

          {/* RIGHT: Actions and close */}
          <Flex align="center" gap={2} justify="flex-end" flex="1">
            {enableToolbar && (
            <><IconButton
              icon={<DownloadIcon />}
              aria-label="Download"
              size="sm"
              onClick={() => handleDownload(url)}
              _hover={{ bg: 'primary.400' }}
              variant="ghost"
              color="white"
            />
              <IconButton
                icon={<PrintIcon />}
                aria-label="Print"
                size="sm"
                _hover={{ bg: 'primary.400' }}
                variant="ghost"
                color="white"
                onClick={() => handlePrint(url)}
              />
            </>
            )}
            <IconButton
              icon={<CloseIcon boxSize={3} />}
              aria-label="Close"
              size="sm"
              variant="ghost"
              bg="white"
              m={1}
              borderRadius="50%"
              _hover={{ bg: 'red' }}
              color="white"
              onClick={onClose}
            />
          </Flex>
        </Flex>
        <ModalBody
          bg="gray.100"
          p={0}
          overflow="auto"
          ref={containerRef}
        >
          <Box p={4} minW="fit-content" display="flex" justifyContent="center">
            <DocumentView fileData={fileData} zoom={scale} rotate={rotate} />
          </Box>
        </ModalBody>
      </ModalContent>
    </Modal>
  );
}

export default DocumentModal;
