import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.alert-modal': {
    borderRadius: '16px !important',
    padding: '0 !important',
    '@media (max-width: 640px)': {
      width: '95%!important',
      margin: '16px !important'
    },
    '@media (max-width: 480px)': {
      width: '90%!important',
      margin: '8px !important'
    },
    '.alert-header': {
      display: 'none !important' // Hide the header completely for the new design
    },
    '.chakra-modal__body': {
      padding: '32px 24px 24px 24px !important',
      textAlign: 'center',
      position: 'relative',
      '@media (max-width: 480px)': {
        padding: '24px 16px 16px 16px !important'
      }
    },
    '.chakra-modal__footer': {
      background: 'transparent !important',
      padding: '0 24px 32px 24px !important',
      borderRadius: '0 !important',
      justifyContent: 'center',
      gap: '12px',
      flexDirection: 'row',
      '@media (max-width: 480px)': {
        flexDirection: 'column',
        gap: '8px'
      }
    },
    '.alert-message': {
      fontSize: '16px',
      textAlign: 'center',
      padding: '16px 0 0 0',
      fontWeight: '400',
      color: colors.gray[700],
      lineHeight: '1.5',
      margin: '0',
      '@media (max-width: 480px)': {
        fontSize: '14px',
        padding: '12px 0 0 0',
        lineHeight: '1.6'
      }
    },
    '.icon-cover': {
      textAlign: 'center',
      '.icon-wrapper': {
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '74px',
        height: '74px',
        borderRadius: '50%',
        marginBottom: '0',
        position: 'relative',
        '&.success': {
          backgroundColor: colors.green[50],
          '&::before': {
            content: '""',
            position: 'absolute',
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            backgroundColor: colors.green[100],
            zIndex: 1
          },
          '& svg': {
            position: 'relative',
            zIndex: 2,
            color: colors.green[600]
          }
        },
        '&.error': {
          backgroundColor: colors.red[50],
          '&::before': {
            content: '""',
            position: 'absolute',
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            backgroundColor: colors.red[100],
            zIndex: 1
          },
          '& svg': {
            position: 'relative',
            zIndex: 2,
            color: colors.red[600]
          }
        },
        '&.warning': {
          backgroundColor: colors.orange[50],
          '&::before': {
            content: '""',
            position: 'absolute',
            width: '48px',
            height: '48px',
            borderRadius: '50%',
            backgroundColor: colors.orange[100],
            zIndex: 1
          },
          '& svg': {
            position: 'relative',
            zIndex: 2,
            color: colors.orange[600]
          }
        }
      }
    },
    '.alert-close': {
      position: 'absolute',
      right: '16px',
      top: '16px',
      zIndex: 10,
      background: 'transparent !important',
      border: 'none !important',
      color: colors.gray[400],
      fontSize: '20px',
      width: '24px',
      height: '24px',
      minWidth: '24px',
      padding: '0',
      '@media (max-width: 480px)': {
        width: '32px',
        height: '32px',
        minWidth: '32px',
        right: '12px',
        top: '12px',
        fontSize: '18px'
      },
      '&:hover': {
        background: `${colors.gray[100]} !important`,
        color: colors.gray[600]
      }
    }
  }

};

export default { component, style };
