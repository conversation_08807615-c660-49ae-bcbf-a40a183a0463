import {
  <PERSON>, FormController, Grid, GridI<PERSON>, <PERSON><PERSON>, t, TitledCard
} from 'common/components';
import { FormLabel, StepperButtons } from 'common/custom-components';
import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSelector } from 'react-redux';
import { useFetchDistrictQuery, useFetchStateQuery } from 'pages/common/api';
import { STATE } from 'common/constants';
import { _ } from 'utils/lodash';
import { academicDetailsSchema } from '../validation/academicDetails';
import {
  ACADEMIC_YEAR_OPTIONS, BOARD_OPTIONS, INSTITUTION_TYPE_OPTIONS,
  YEAR_OPTIONS, COURSE_NAME_OPTIONS, INSTRUCTION_TYPE_OPTIONS
} from '../constants';
import { useGetAcademicDetailsQuery, useSaveAcademicDetailsMutation } from '../api';

const AcademicDetails = ({ onNext, onPrevious }) => {
  const [saveAcademicDetails] = useSaveAcademicDetailsMutation();

  const applicationId = useSelector(
    (state) => state.application.formData?.personalDetails?.applicationId
  );

  // Fetch academic details if they exist
  const { data: academicDetails } = useGetAcademicDetailsQuery(applicationId, {
    skip: !applicationId
  });

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(academicDetailsSchema),
    defaultValues: {
      // Common values
      currentInstitutionType: '',
      currentInstitutionName: '',
      courseMode: '',
      academicYear: '',
      dateOfAdmission: '',

      // Class 10 values
      board: '',
      gradePercentage: '',
      institutionName: '',
      institutionType: '',
      institutionLocation: '',
      yearOfCompletion: '',
      stateOfInstitution: '',
      districtOfInstitution: '',
      marksObtained: '',
      totalMarks: '',
      percentage: '',

      // Higher secondary values
      hsBoard: '',
      hsGradePercentage: '',
      hsInstitutionName: '',
      hsYearOfCompletion: '',
      hsStateOfInstitution: '',
      hsDistrictOfInstitution: '',

      // Under graduation values
      underGradCourseName: '',
      instructionType: '',
      instructionLocation: '',
      instructionName: '',
      competitiveExam: '',
      underGradYearOfCompletion: '',
      underGradStateOfInstitution: '',
      underGradDistrictOfInstitution: '',
      gradingSystem: ''
    }
  });

  const { data: stateOptions = [] } = useFetchStateQuery(STATE.id);
  const { data: districtOptions = [] } = useFetchDistrictQuery(STATE.id);

  const scholarshipType = '10th'; // Or get from redux: useSelector(state => state.scholarshipType);

  // Pre-fill form when academic details are loaded
  useEffect(() => {
    if (academicDetails) {
      reset(academicDetails);
    }
  }, [academicDetails, reset]);

  const onSubmit = async (data) => {
    // Save to API
    await saveAcademicDetails({
      applicationId,
      ...data
    }).unwrap();

    // Proceed to next step
    if (onNext) {
      onNext(data);
    }
  };

  const renderClass10Section = () => (
    <TitledCard title={t('academicDetails')}>
      <Box p={6}>
        <Heading size="md" mb={6} color="gray.700">
          {t('class10BoardExam')}
        </Heading>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('board')}
              name="board"
              control={control}
              errors={errors}
              options={BOARD_OPTIONS}
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('gradePercentage')}
              name="gradePercentage"
              control={control}
              errors={errors}
              placeholder={t('enterGradePercentage')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('institutionName')}
              name="institutionName"
              control={control}
              errors={errors}
              placeholder={t('institutionNamePlaceholder')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('institutionType')}
              name="institutionType"
              control={control}
              errors={errors}
              options={INSTITUTION_TYPE_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('institutionLocation')}
              name="institutionLocation"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="yearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('stateOfInstitution')}
              name="stateOfInstitution"
              control={control}
              errors={errors}
              options={_.get(stateOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="districtOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('marksObtained')}
              name="marksObtained"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('totalMarks')}
              name="totalMarks"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('percentage')}
              name="percentage"
              control={control}
              errors={errors}
              placeholder={t('enterPercentage')}
              required
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );

  const renderHigherSecondarySection = () => (
    <TitledCard title={t('higherSecondaryDetails')} mt={8}>
      <Box p={6}>
        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('board')}
              name="hsBoard"
              control={control}
              errors={errors}
              options={BOARD_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('gradePercentage')}
              name="hsGradePercentage"
              control={control}
              errors={errors}
              placeholder={t('enterGradePercentage')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('institutionName')}
              name="hsInstitutionName"
              control={control}
              errors={errors}
              placeholder={t('institutionNamePlaceholder')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="hsYearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('stateOfInstitution')}
              name="hsStateOfInstitution"
              control={control}
              errors={errors}
              options={_.get(stateOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="hsDistrictOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );

  const renderUnderGraduationSection = () => (
    <TitledCard title={t('academicDetails')} mt={8}>
      <Box p={6}>
        <Heading size="md" mb={6} color="gray.700">
          {t('higherSecondaryDetails')}
        </Heading>
        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('courseName')}
              name="underGradCourseName"
              control={control}
              errors={errors}
              options={COURSE_NAME_OPTIONS}
              optionKey="code"
              required
              placeholder={t('selectCourseName')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('instructionType')}
              name="instructionType"
              control={control}
              errors={errors}
              options={INSTRUCTION_TYPE_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('instructionLocation')}
              name="instructionLocation"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('instructionName')}
              name="instructionName"
              control={control}
              errors={errors}
              placeholder={t('institutionName')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('competitiveExam')}
              name="competitiveExam"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('yearOfCompletion')}
              name="underGradYearOfCompletion"
              control={control}
              errors={errors}
              options={YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('stateOfInstitution')}
              name="underGradStateOfInstitution"
              control={control}
              errors={errors}
              options={_.get(stateOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="select"
              label={t('districtOfInstitution')}
              name="underGradDistrictOfInstitution"
              control={control}
              errors={errors}
              options={_.get(districtOptions, 'payload', [])}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>
        </Grid>

        <Box mt={6} mb={6}>
          <FormLabel label={t('gradingSystem')} required />
          <Box mt={2} mb={2} fontSize="sm" color="gray.500">
            {t('pleaseSelectOption')}
          </Box>
          <FormController
            type="radio"
            name="gradingSystem"
            control={control}
            errors={errors}
            options={[
              { code: 'mark', name: t('mark') },
              { code: 'cgpa', name: t('cgpa') }
            ]}
            optionKey="code"
            required
            direction="row"
          />
        </Box>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('marksObtained')}
              name="marksObtained"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('totalMarks')}
              name="totalMarks"
              control={control}
              errors={errors}
              placeholder={t('enterHere')}
              required
            />
          </GridItem>

          <GridItem colSpan={[12, 6, 4]}>
            <FormController
              type="text"
              label={t('percentage')}
              name="percentage"
              control={control}
              errors={errors}
              placeholder={t('enterPercentage')}
              required
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );

  const renderCurrentCourseSection = () => (
    <TitledCard title={t('currentCourseDetails')} sx={{ marginTop: '32px' }}>
      <Box p={6}>
        <Box mb={6}>
          <FormLabel label={t('institutionType')} required />
          <Box mt={2} mb={8} fontSize="sm" color="gray.500" />
          <FormController
            type="select"
            name="currentInstitutionType"
            control={control}
            errors={errors}
            options={[
              { code: 'government', name: t('government') },
              { code: 'aided', name: t('aided') },
              { code: 'self-financed', name: t('selfFinanced') },
              { code: 'others', name: t('others') }
            ]}
            optionKey="code"
            placeholder={t('currentInstitutionType')}
            label={t('pleaseSelectOption')}
            required
          />
        </Box>

        <Box mb={6}>
          <FormController
            type="text"
            label={t('institutionName')}
            name="currentInstitutionName"
            control={control}
            errors={errors}
            placeholder={t('enterInstitutionName')}
            required
          />
        </Box>

        <Box mb={6}>
          <FormLabel label={t('courseMode')} required />
          <Box mt={2} mb={2} fontSize="sm" color="gray.500">
            {t('pleaseSelectOption')}
          </Box>
          <FormController
            type="radio"
            name="courseMode"
            control={control}
            errors={errors}
            options={[
              { code: 'regular', name: t('regular') },
              { code: 'correspondence', name: t('correspondence') }
            ]}
            optionKey="code"
            required
            direction="row"
          />
        </Box>

        <Grid templateColumns="repeat(12, 1fr)" gap={6}>
          <GridItem colSpan={[12, 6]}>
            <FormController
              type="select"
              label={t('academicYear')}
              name="academicYear"
              control={control}
              errors={errors}
              options={ACADEMIC_YEAR_OPTIONS}
              optionKey="code"
              required
              placeholder={t('select')}
            />
          </GridItem>

          <GridItem colSpan={[12, 6]}>
            <FormController
              type="date"
              label={t('dateOfAdmission')}
              name="dateOfAdmission"
              control={control}
              errors={errors}
              required
              placeholder="DD/MM/YYYY"
              max={new Date().toISOString().split('T')[0]}
              popperProps={{
                placement: 'top',
                strategy: 'fixed',
                modifiers: [
                  {
                    name: 'offset',
                    options: { offset: [0, 10] }
                  },
                  {
                    name: 'preventOverflow',
                    options: {
                      boundary: 'viewport',
                      altBoundary: true,
                      padding: 10
                    }
                  },
                  {
                    name: 'flip',
                    options: { fallbackPlacements: ['bottom'] }
                  }
                ]
              }}
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );

  const renderAcademicSections = () => {
    switch (scholarshipType) {
      case '10th':
        return (
          <>
            {renderClass10Section()}
            {renderCurrentCourseSection()}
          </>
        );
      case 'higherSecondary':
        return (
          <>
            {renderHigherSecondarySection()}
            {renderCurrentCourseSection()}
          </>
        );
      case 'undergraduate':
        return (
          <>
            {renderUnderGraduationSection()}
            {renderCurrentCourseSection()}
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Box gap={10} as="form" onSubmit={handleSubmit(onSubmit)}>
      {renderAcademicSections()}
      <Box mt={30}>
        <StepperButtons
          currentStep={3}
          totalSteps={6}
          onNext={handleSubmit(onSubmit)}
          onPrevious={onPrevious}
          layout="space-between"
          isNextDisabled={!isValid}
        />
      </Box>
    </Box>
  );
};

export default AcademicDetails;
