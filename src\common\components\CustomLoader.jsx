// CustomLoader.js
import React from 'react';
import { CircularProgress, Box } from 'common/components';

const CustomLoader = () => {
  return (
    <Box
      position="fixed"
      top="0"
      left="0"
      width="100vw"
      height="100vh"
      display="flex"
      justifyContent="center"
      alignItems="center"
      bg="rgba(255, 255, 255, 0.8)"
      backdropFilter="blur(10px)"
      zIndex="1000"
    >
      <CircularProgress
        isIndeterminate
        size="40px"
        thickness="8px"
        trackColor="secondary.500"
        color="primary.500"
      />
    </Box>
  );
};

export default CustomLoader;
