import { <PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON>White } from 'assets/images';
import { Book } from 'assets/svg';
import {
  Box, Flex, Text, Image, t
} from 'common/components';

const DashboardBanner = ({
  name = '',
  subtitle = 'defaultSubtitle',
  title
}) => {
  let heading;
  if (title) {
    heading = t(title);
  } else if (name) {
    heading = t('welcomeBack', { name });
  } else {
    heading = t('welcome');
  }

  return (
    <Box
      // bgGradient="linear(to-r, primary.50, primary.700)"
      bg="primary.500"
      borderRadius="lg"
      w="100%"
      overflow="hidden"
      mb={4}
      position="relative"
      h={{ base: '100px', md: '170px' }}
    >
      <Flex
        direction={{ base: 'column', md: 'row' }}
        align="center"
        justify="space-between"
        h={{ base: '100px', md: '170px' }}
        position="relative"
      >
        {/* Left Section */}
        <Box
          color="white"
          maxW={{ base: '100%', md: '50%' }}
          p={{ base: 4, md: 6 }}
          py={{ base: 3, md: 4 }}
          zIndex={2}
          position="relative"
        >
          {/* RP Logo - Hidden on mobile */}
          <Image
            src={RPLogoWhite}
            alt="Portal Logo"
            maxH="35px"
            mb={3}
            display={{ base: 'none', md: 'block' }}
          />
          <Text
            fontSize={{ base: 'xl', md: '2xl' }}
            fontWeight="bold"
            color="white"
          >
            {heading}
          </Text>
          <Text fontSize="sm" color="white" mt={1} fontWeight="400" opacity={0.9}>
            {t(subtitle)}
          </Text>
        </Box>

        {/* Right Section: Images - Positioned absolutely to remove padding */}
        <Box
          position="absolute"
          right={0}
          top={0}
          bottom={0}
          width={{ base: '100%', md: '50%' }}
          display={{ base: 'none', md: 'block' }}
          overflow="hidden"
        >
          {/* Books Image - Positioned to overlap slightly */}
          <Image
            src={Book}
            alt="Graduation Cap"
            position="absolute"
            right="215px"
            top="0"
            bottom="0"
            height="100%"
            width="auto"
            objectFit="contain"
            zIndex={2}
          />
          {/* Corner Image - Background positioned */}
          <Image
            src={BannerCorner}
            alt="Banner-corner"
            position="absolute"
            right="0"
            top="0"
            bottom="0"
            height="100%"
            width="auto"
            objectFit="contain"
            zIndex={1}
          />
        </Box>
      </Flex>
    </Box>
  );
};

export default DashboardBanner;
