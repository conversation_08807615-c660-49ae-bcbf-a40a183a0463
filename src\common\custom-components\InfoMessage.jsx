import { InfoSvg } from 'assets/svg';
import { Tooltip } from 'common/components';

const InfoMessage = ({ text, placement = 'top' }) => {
  return (
    <Tooltip
      as="span"
      display="inline-block"
      placement={placement}
      initial={{ x: '25vw' }}
      animate={{ x: 0 }}
      hasArrow
      borderRadius="6px"
      label={text}
      bg="primary.500"
    >
      <div>
        <InfoSvg />
      </div>
    </Tooltip>
  );
};

export default InfoMessage;
