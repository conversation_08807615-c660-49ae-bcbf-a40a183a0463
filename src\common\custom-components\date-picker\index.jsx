import React, { useState, useEffect, useRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Calender } from 'assets/svg';
import ErrorText from '../error-text';
import FormLabel from '../form-label';
import CustomHeader from './CustomHeader';

const getYear = (date) => new Date(date).getFullYear();
const getMonth = (date) => new Date(date).getMonth();

const DatePickerComponent = ({
  label,
  name,
  onChange,
  disabled,
  error,
  value,
  type,
  timeFormat = 'hh:mm aa',
  ellipsis,
  required,
  placeholder,
  timePlaceholder,
  readOnly,
  dateFormat = 'dd-MM-yyyy',
  timeInterval = 30,
  fromYear = 1900,
  toYear = getYear(new Date()),
  ...rest
}) => {
  const [startDate, setStartDate] = useState('');
  const datePickerRef = useRef(null);

  useEffect(() => {
    setStartDate(value);
  }, [value]);

  const handleChange = (date) => {
    if (!readOnly) {
      setStartDate(date);
      onChange(date);
    }
  };

  return (
    <div className="datePicker__container">
      {label && (
        <FormLabel
          disabled={disabled || readOnly}
          label={label}
          required={required}
          ellipsis={ellipsis}
        />
      )}

      {type === 'time' ? (
        <DatePicker
          id={name}
          name={name}
          selected={startDate}
          onChange={handleChange}
          disabled={disabled}
          showTimeSelect
          showTimeSelectOnly
          timeIntervals={timeInterval}
          timeCaption=""
          wrapperClassName={
            error ? 'Norka-calendar-wrapper error' : 'Norka-calendar-wrapper'
          }
          calendarClassName="Norka-time"
          popperClassName="Norka-time-popper"
          ref={datePickerRef}
          dateFormat={timeFormat}
          placeholderText={timePlaceholder || 'HH: MM AA'}
          {...rest}
        />
      ) : (
        <DatePicker
          id={name}
          wrapperClassName={
            error ? 'Norka-calendar-wrapper error' : 'Norka-calendar-wrapper'
          }
          calendarClassName="Norka-calendar"
          popperClassName="Norka-calendar-popper"
          renderCustomHeader={(headerProp) => {
            return (
              <CustomHeader
                headerProp={headerProp}
                fromYear={fromYear}
                toYear={toYear}
                getYear={getYear}
                getMonth={getMonth}
              />
            );
          }}
          formatWeekDay={(nameOfDay) => nameOfDay.substring(0, 1)}
          name={name}
          selected={startDate}
          onChange={handleChange}
          dateFormat={dateFormat}
          disabled={disabled}
          placeholderText={placeholder || 'DD-MM-YYYY'}
          ref={datePickerRef}
          {...rest}
        />
      )}
      <button
        type="button"
        className="icon-wrapper"
        onClick={() => datePickerRef.current.setFocus(true)}
      >
        <Calender />
      </button>
      {!disabled && error && <ErrorText error={error} />}
    </div>
  );
};

export default DatePickerComponent;
