import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  Box, TitledCard, CustomAlert, t, FormController, Grid
} from 'common/components';
import { GuideLines } from 'assets/svg';
import { FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';
import { getFileContent } from 'pages/others/fileDownload/helpers';
import { StepperButtons } from 'common/custom-components';
import { documentSchema } from '../validation/document';

const Documents = ({
  isHSSApplicant = false,
  isPGApplicant = false,
  hasSpecialStatus = false,
  onNext,
  onPrevious
}) => {
  const {
    control, setValue, handleSubmit, formState: { errors }
  } = useForm({
    mode: 'all',
    resolver: yupResolver(documentSchema),
    defaultValues: {
      isHSSApplicant,
      isPGApplicant,
      hasSpecialStatus
    }
  });

  const [previewUrls, setPreviewUrls] = useState({
    aadhaar: [],
    passport: [],
    marksheet: [],
    ugMarksheet: [],
    scoreCard: [],
    incomeCertificate: [],
    specialCertificate: [],
    achievementCertificate: [],
    bankPassbook: [],
    rationCard: []
  });

  const handleFileUpload = (file, documentType) => {
    if (!file) return;
    setValue(documentType, file);
    if (file.size < FILE_SIZE && VALID_FILE_TYPE.includes(file?.type)) {
      setPreviewUrls((prev) => ({ ...prev, [documentType]: [getFileContent(file)] }));
    }
  };

  const handleRemoveFile = (documentType) => {
    setValue(documentType, null);
    if (previewUrls[documentType]) {
      URL.revokeObjectURL(previewUrls[documentType]);
      setPreviewUrls((prev) => ({ ...prev, [documentType]: [] }));
    }
  };

  const formSubmit = (data) => {
    if (onNext) {
      onNext(data);
    }
  };

  return (
    <form onSubmit={handleSubmit(formSubmit)}>
      <TitledCard title={t('documentUpload')}>
        <Box p={6}>
          <Grid templateColumns="repeat(1, 1fr)" gap={4}>
            <Box mb={9}>
              <CustomAlert
                label={t('documentGuidelinesTitle')}
                message={t('documentGuidelinesMessage')}
                bg="orange.50"
                iconColor="orange.500"
                textColor="orange.700"
                icon={GuideLines}
              />
            </Box>

            <FormController
              type="file"
              required
              name="aadhaar"
              control={control}
              showPreview
              errors={errors}
              previewData={previewUrls.aadhaar || []}
              handleChange={(file) => handleFileUpload(file, 'aadhaar')}
              onFileRemove={() => handleRemoveFile('aadhaar')}
              label={t('aadhaarCard')}
              description={t('aadhaarDescription')}
            />

            <FormController
              showPreview
              type="file"
              name="passport"
              label={t('passportPhoto')}
              required
              description={t('passportDescription')}
              previewData={previewUrls.passport}
              errors={errors}
              handleChange={(file) => handleFileUpload(file, 'passport')}
              onFileRemove={() => handleRemoveFile('passport')}
              control={control}
            />

            {isHSSApplicant && (
              <FormController
                showPreview
                type="file"
                name="marksheet"
                label={t('sslcMarksheet')}
                required
                description={t('sslcMarksheetDescription')}
                previewData={previewUrls.marksheet}
                errors={errors}
                handleChange={(file) => handleFileUpload(file, 'marksheet')}
                onFileRemove={() => handleRemoveFile('marksheet')}
                control={control}
              />
            )}

            {isPGApplicant && (
              <>
                <FormController
                  showPreview
                  type="file"
                  name="ugMarksheet"
                  label={t('ugMarksheet')}
                  required
                  description={t('ugMarksheetDescription')}
                  previewData={previewUrls.ugMarksheet}
                  errors={errors}
                  handleChange={(file) => handleFileUpload(file, 'ugMarksheet')}
                  onFileRemove={() => handleRemoveFile('ugMarksheet')}
                  control={control}
                />

                <FormController
                  showPreview
                  type="file"
                  name="scoreCard"
                  label={t('scoreCard')}
                  required
                  description={t('scoreCardDescription')}
                  previewData={previewUrls.scoreCard}
                  errors={errors}
                  handleChange={(file) => handleFileUpload(file, 'scoreCard')}
                  onFileRemove={() => handleRemoveFile('scoreCard')}
                  control={control}
                />
              </>
            )}

            <FormController
              showPreview
              type="file"
              name="incomeCertificate"
              label={t('incomeCertificate')}
              required
              description={t('incomeCertificateDescription')}
              previewData={previewUrls.incomeCertificate}
              errors={errors}
              handleChange={(file) => handleFileUpload(file, 'incomeCertificate')}
              onFileRemove={() => handleRemoveFile('incomeCertificate')}
              control={control}
            />

            <FormController
              showPreview
              type="file"
              name="bankPassbook"
              label={t('bankPassbook')}
              required
              description={t('bankPassbookDescription')}
              previewData={previewUrls.bankPassbook}
              errors={errors}
              handleChange={(file) => handleFileUpload(file, 'bankPassbook')}
              onFileRemove={() => handleRemoveFile('bankPassbook')}
              control={control}
            />

            <FormController
              showPreview
              type="file"
              name="rationCard"
              label={t('rationCard')}
              required
              description={t('rationCardDescription')}
              previewData={previewUrls.rationCard}
              errors={errors}
              handleChange={(file) => handleFileUpload(file, 'rationCard')}
              onFileRemove={() => handleRemoveFile('rationCard')}
              control={control}
            />

            {hasSpecialStatus && (
              <FormController
                showPreview
                type="file"
                name="specialCertificate"
                label={t('specialCertificate')}
                required
                description={t('specialCertificateDescription')}
                previewData={previewUrls.specialCertificate}
                errors={errors}
                handleChange={(file) => handleFileUpload(file, 'specialCertificate')}
                onFileRemove={() => handleRemoveFile('specialCertificate')}
                control={control}
              />
            )}

            <FormController
              showPreview
              type="file"
              name="achievementCertificate"
              label={t('achievementCertificate')}
              description={t('achievementCertificateDescription')}
              previewData={previewUrls.achievementCertificate}
              errors={errors}
              handleChange={(file) => handleFileUpload(file, 'achievementCertificate')}
              onFileRemove={() => handleRemoveFile('achievementCertificate')}
              control={control}
            />
          </Grid>
        </Box>
      </TitledCard>
      {/* Action Buttons */}
      <Box mt={6}>
        <StepperButtons
          currentStep={4}
          totalSteps={6}
          onNext={onNext}
          onPrevious={onPrevious}
          layout="space-between"
        />
      </Box>
    </form>
  );
};

export default Documents;
