export const PrintIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M7 8V4C7 3.448 7.448 3 8 3H16C16.552 3 17 3.448 17 4V8" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 17H5C3.895 17 3 16.105 3 15V10C3 8.895 3.895 8 5 8H19C20.105 8 21 8.895 21 10V15C21 16.105 20.105 17 19 17H17" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M7 13.7998H17V19.9998C17 20.5518 16.552 20.9998 16 20.9998H8C7.448 20.9998 7 20.5518 7 19.9998V13.7998Z" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 11H8" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const DownloadIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M15.9997 4V22.6667" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 18.667L16 22.667L20 18.667" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6667 13.333H6.66667C5.19333 13.333 4 14.5263 4 15.9997V25.333C4 26.8063 5.19333 27.9997 6.66667 27.9997H25.3333C26.8067 27.9997 28 26.8063 28 25.333V15.9997C28 14.5263 26.8067 13.333 25.3333 13.333H21.3333" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const RotateIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_874_7517)">
      <path d="M7.34035 6.40977L0.860352 12.8998L7.35035 19.3798L13.8404 12.8998L7.34035 6.40977ZM3.69035 12.8998L7.35035 9.23977L11.0004 12.8998L7.34035 16.5598L3.69035 12.8998ZM19.3604 6.63977C17.6104 4.87977 15.3004 3.99977 13.0004 3.99977V0.759766L8.76035 4.99977L13.0004 9.23977V5.99977C14.7904 5.99977 16.5804 6.67977 17.9504 8.04977C20.6804 10.7798 20.6804 15.2198 17.9504 17.9498C16.5804 19.3198 14.7904 19.9998 13.0004 19.9998C12.0304 19.9998 11.0604 19.7898 10.1604 19.3898L8.67035 20.8798C10.0204 21.6198 11.5104 21.9998 13.0004 21.9998C15.3004 21.9998 17.6104 21.1198 19.3604 19.3598C22.8804 15.8498 22.8804 10.1498 19.3604 6.63977Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_874_7517">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ZoomInIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_874_7514)">
      <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_874_7514">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const ZoomOutIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <g clipPath="url(#clip0_874_7511)">
      <path d="M19 13H5V11H19V13Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_874_7511">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CloudFailed = (props) => (
  <svg width="56" height="56" viewBox="0 0 56 56" fill="none" {...{ ...props }} xmlns="http://www.w3.org/2000/svg">
    <path d="M25.0833 43.75L30.9166 49.5833" stroke="#E8526D" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M25.0833 49.5833L30.9166 43.75" stroke="#E8526D" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M37.3333 46.6667H46.6666" stroke="#E8526D" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.33331 46.6667H18.6666" stroke="#E8526D" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M28 35.0449V38.5449" stroke="#E8526D" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M18.0006 35.0011H39.6666C44.177 35.0011 47.8333 31.3447 47.8333 26.8344C47.8333 22.3241 44.177 18.6677 39.6666 18.6677C39.6826 12.8895 35.4555 7.9751 29.7398 7.127C24.0241 6.2789 18.5521 9.75414 16.8896 15.2881C11.6859 15.9071 7.86811 20.4771 8.18486 25.7078C8.50161 30.9386 12.843 35.0145 18.0833 35.0011" fill="#E83A7A" fillOpacity="0.6" />
    <path d="M18.0006 35.0011H39.6666C44.177 35.0011 47.8333 31.3447 47.8333 26.8344C47.8333 22.3241 44.177 18.6677 39.6666 18.6677C39.6826 12.8895 35.4555 7.9751 29.7398 7.127C24.0241 6.2789 18.5521 9.75414 16.8896 15.2881C11.6859 15.9071 7.86811 20.4771 8.18486 25.7078C8.50161 30.9386 12.843 35.0145 18.0833 35.0011" stroke="#E83A7A" strokeWidth="3.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);
