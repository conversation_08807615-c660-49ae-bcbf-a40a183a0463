import React, { useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Box } from 'common/components';
import { startTokenValidation, stopTokenValidation } from 'utils/tokenValidator';

function App() {
  useEffect(() => {
    startTokenValidation();
    return () => {
      stopTokenValidation();
    };
  }, []);

  return (
    <Box h="100vh" overflow="hidden">
      <Box overflowY="auto" h="full">
        <Outlet />
      </Box>
    </Box>
  );
}

export default App;
