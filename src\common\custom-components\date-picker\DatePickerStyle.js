import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.datePicker__container': {
    width: '100%',
    height: '50px',
    position: 'relative',
    'p.form-label': {
      position: 'absolute',
      top: '-8px',
      background: `${colors.white} !important`,
      padding: '0 5px',
      left: '13px',
      fontSize: '13px',
      lineHeight: '14px',
      fontWeight: 400,
      zIndex: 2
    },
    '.icon-wrapper': {
      position: 'absolute',
      right: '16px',
      top: '50%',
      transform: 'translateY(-50%)'
    },
    '.Norka-calendar-wrapper': {
      width: '100%',
      height: '100%',
      borderRadius: '8px',
      '&.error': {
        border: `1px solid ${colors.red[500]} !important`
      },
      '.react-datepicker__input-container': {
        width: '100%',
        height: '100%',
        ':focus-visible': {
          outline: 'none'
        },
        input: {
          width: '100%',
          height: '100%',
          padding: '0 16px',
          background: `${colors.white} !important`,
          color: 'gray.700 !important',
          fontStyle: 'normal',
          outline: 'none',
          fontWeight: '400 !important',
          border: `1px solid ${colors.gray[300]}`,
          borderRadius: '8px',
          fontSize: '16px !important',
          lineHeight: '16px',
          transition: 'all 150ms ease-in-out',
          '::placeholder': {
            fontStyle: 'normal',
            fontWeight: '400 !important',
            fontSize: '14px !important',
            lineHeight: '16px',
            opacity: '0.3'
          },
          ':is(:disabled, :read-only)': {
            opacity: '1 !important',
            border: `1px solid ${colors.gray[100]} !important`
          },
          '&.error': {
            border: `1px solid ${colors.red[500]} !important`
          },
          ':is(:hover, :focus)': {
            border: `1px solid ${colors.tertiary[500]}`,
            transition: 'all 150ms ease-in-out'
          }
        }
      }
    },
    '.Norka-calendar-popper': {
      paddingTop: '0px',
      zIndex: 99,
      width: '413px',
      '@media (max-width: 640px)': {
        width: '100%'
      },
      '.Norka-calendar': {
        width: '100%',
        outline: 'none',
        border: '0',
        borderRadius: '8px',
        background: colors.white,
        boxShadow: '0px 0px 7px 0px rgba(0, 0, 0, 0.10)',
        padding: '20px 51px',
        '@media (max-width: 640px)': {
          padding: '20px'
        },
        '.react-datepicker__month-container': {
          width: '100%',
          '.react-datepicker__header': {
            background: colors.white,
            padding: 0,
            borderBottom: 0,
            marginBottom: '24px',
            '.Norka-calendar-headWrapper': {
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '24px',
              position: 'relative',
              '.Norka-calendar-headSelector': {
                display: 'flex',
                justifyContent: 'flex-start',
                alignItems: 'center',
                button: {
                  display: 'flex',
                  justifyContent: 'flex-start',
                  alignItems: 'center',
                  columnGap: '10px',
                  color: colors.tertiary[500],
                  fontSize: '16px',
                  fontWeight: 600,
                  lineHeight: '16px',
                  letterSpacing: '0.594px',
                  cursor: 'pointer'
                }
              },
              '.Norka-calendar-headButtons': {
                display: 'flex',
                justifyContent: 'flex-end',
                alignItems: 'center',
                columnGap: '6px'
              }
            },
            '.react-datepicker__day-names': {
              width: '93%',
              display: 'flex',
              margin: 'auto',
              justifyContent: 'space-between',
              alignItems: 'center',
              '.react-datepicker__day-name': {
                width: '11px',
                userSelect: 'none',
                height: '20px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                margin: 0,
                color: '#6A7993',
                fontSize: '16px',
                fontWeight: 500
              }
            }
          },
          '.react-datepicker__month': {
            width: '100%',
            margin: 0,
            '&:has(.Norka-calendar-showYear, .Norka-calendar-showMonth) .react-datepicker__week': {
              position: 'absolute',
              opacity: 0,
              visibility: 'hidden',
              zIndex: '-1',
              transition: 'all 150ms ease-in-out'
            },
            '.react-datepicker__week': {
              position: 'relative',
              width: '100%',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              opacity: 1,
              visibility: 'visible',
              zIndex: '3',
              transition: 'all 150ms ease-in-out',
              '&:not(:last-of-type)': {
                marginBottom: '14px'
              },
              '.react-datepicker__day': {
                width: '32px',
                userSelect: 'none',
                height: '32px',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                margin: 0,
                color: '#454545',
                borderRadius: '99px',
                fontSize: '16px',
                fontWeight: 500,
                lineHeight: '16px',
                transition: 'all 150ms ease-in-out',
                '&:hover, &.react-datepicker__day--selected, &.react-datepicker__day--keyboard-selected': {
                  background: '#00B2EC',
                  color: '#fff !important'
                },
                '&.react-datepicker__day--outside-month': {
                  color: '#E1E4E7'
                },
                '&.react-datepicker__day--disabled': {
                  color: '#E1E4E7',
                  background: '#fff',
                  cursor: 'not-allowed'
                }
              }
            }
          }
        }
      }
    },
    '.react-datepicker-popper[data-placement^=bottom] .react-datepicker__triangle': {
      display: 'none'
    },
    '.react-datepicker-popper[data-placement^=top] .react-datepicker__triangle': {
      display: 'none'
    },
    '.Norka-time-popper': {
      paddingTop: '0px',
      zIndex: 99,
      left: '50% !important',
      transform: 'translate(-50%, 44px) !important',
      width: '280px',
      '&[data-placement^=top]': {
        padding: '0 !important',
        bottom: '100% !important',
        transform: 'translate(-50%, 0) !important'
      },
      '.Norka-time': {
        width: '100%',
        background: colors.white,
        boxShadow: '0px 0px 7px 0px rgba(0, 0, 0, 0.10)',
        border: `1px solid ${colors.blue[30]}`,
        '.react-datepicker__time-container': {
          width: '100%',
          '.react-datepicker__header': {
            display: 'none'
          },
          '.react-datepicker__time-box': {
            margin: 0,
            width: '100%',
            '.react-datepicker__time-list-item': {
              textAlign: 'left',
              fontSize: '16px',
              height: 'max-content',
              width: '100%',
              color: '#454545',
              fontWeight: 500,
              padding: '8px 12px',
              borderRadius: '8px',
              transition: 'all 150ms ease-in-out',
              '&.react-datepicker__time-list-item--selected, &:hover': {
                background: '#00B2EC',
                color: '#fff !important'
              }
            }
          }
        }
      }
    },
    '.Norka-calendar-showYear': {
      width: '360px',
      margin: '0px -25px',
      minHeight: '200px',
      height: '200px',
      maxHeight: '200px',
      paddingBottom: '14px',
      transition: 'all 150ms ease-in-out',
      background: colors.white,
      overflowY: 'auto',
      display: 'grid',
      gridTemplateColumns: 'repeat(4, 1fr)',
      gap: '30px 14px',
      '@media (max-width: 640px)': {
        width: '100%',
        margin: '0px 10px',
        gap: '15px 10px',
        boxShadow: '0px 0px 7px 0px rgba(0, 0, 0, 0.10)',
        padding: '15px'
      },
      button: {
        width: '72px',
        height: '28px',
        borderRadius: '99px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        fontSize: '16px',
        fontWeight: 700,
        color: '#9AA6AD',
        lineHeight: '16px',
        letterSpacing: '0.594px',
        cursor: 'pointer',
        transition: 'all 150ms ease-in-out',
        '&:hover, &.active': {
          background: colors.primary[500],
          color: colors.white
        },
        '@media (max-width: 640px)': {
          width: 'auto',
          height: 'auto',
          padding: '8px',
          fontSize: '14px'
        }
      }
    },
    '.Norka-calendar-showMonth': {
      width: '380px',
      margin: '0px -30px',
      minHeight: '200px',
      height: '200px',
      maxHeight: '200px',
      transition: 'all 150ms ease-in-out',
      background: colors.white,
      overflowY: 'auto',
      display: 'grid',
      gridTemplateColumns: 'repeat(3, 1fr)',
      gap: '30px 0',
      '@media (max-width: 640px)': {
        width: '100%',
        margin: '0px 10px',
        gap: '10px',
        boxShadow: '0px 0px 7px 0px rgba(0, 0, 0, 0.10)',
        padding: '15px'
      },
      button: {
        width: '120px',
        height: '28px',
        borderRadius: '99px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        textAlign: 'center',
        fontSize: '16px',
        fontWeight: 700,
        color: '#9AA6AD',
        lineHeight: '16px',
        letterSpacing: '0.594px',
        cursor: 'pointer',
        transition: 'all 150ms ease-in-out',
        '&:hover, &.active': {
          background: colors.primary[500],
          color: colors.white
        },
        '@media (max-width: 640px)': {
          width: 'auto',
          height: 'auto',
          margin: '5px',
          fontSize: '14px'
        }
      }
    }
  }
};

export default { component, style };
