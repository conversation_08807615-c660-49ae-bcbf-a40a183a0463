import { sampleApi } from 'pages/scholar/api';
import { authApi } from 'pages/auth/api';
import { commonApi } from 'pages/common/api';
import { documentApi } from 'pages/others/fileDownload/api';
import { applicationApi } from 'pages/applicant/application/api';
import { reducer as commonReducer } from 'pages/common/slice';
import { reducer as fileReducer } from 'pages/others/fileDownload/slice';
import { reducer as applicationReducer } from 'pages/applicant/application/slice';

export const apiSlices = [authApi, commonApi, applicationApi, documentApi, sampleApi];

export const sliceReducers = {
  common: commonReducer,
  'file-download': fileReducer,
  application: applicationReducer
};
