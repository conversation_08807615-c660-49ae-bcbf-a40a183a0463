import { WarningIcon } from 'assets/svg';
import {
  Box, Flex, Icon, t, Text
} from 'common/components';

const CustomAlert = ({
  title = 'importantNote',
  message = 'familyIncomeCriteria',
  icon = WarningIcon,
  bg = 'yellow.50',
  iconColor = 'yellow.500',
  textColor = 'yellow.600'
}) => {
  return (
    <Flex
      bg={bg}
      p={4}
      borderRadius="lg"
      align="flex-start"
      gap={3}
      fontSize="sm"
      border="1px solid"
      borderColor="yellow.200"
    >
      <Icon as={icon} boxSize={6} mt={1} color={iconColor} />
      <Box>
        <Text fontWeight="semibold" color={textColor} fontSize={['sm', 'md']}>
          {t(title)}
        </Text>
        <Text mt={1} color={textColor} fontSize={['10px', '12px']}>
          {t(message)}
        </Text>
      </Box>
    </Flex>
  );
};

export default CustomAlert;
