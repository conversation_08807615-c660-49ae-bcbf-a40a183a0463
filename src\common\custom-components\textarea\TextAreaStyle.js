import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.text-area__container': {
    position: 'relative',
    width: '100%',
    'p.form-label': {
      position: 'absolute',
      top: '-7px',
      background: `${colors.white} !important`,
      padding: '0 5px',
      left: '13px',
      fontSize: '13px',
      lineHeight: '14px',
      fontWeight: 400,
      zIndex: 2
    },
    textarea: {
      padding: '18px',
      width: '100%',
      height: '100%',
      color: 'gray.700 !important',
      fontStyle: 'normal',
      fontWeight: '400 !important',
      background: `${colors.white} !important`,
      border: `1px solid ${colors.gray[300]}`,
      borderRadius: '8px',
      fontSize: '16px !important',
      lineHeight: '16px',
      outline: '0',
      margin: '0',
      transition: 'all 150ms ease-in-out',
      '::placeholder': {
        fontStyle: 'normal',
        fontWeight: '400 !important',
        fontSize: '14px !important',
        lineHeight: '16px',
        opacity: '0.3'
      },
      ':is(:disabled, :read-only)': {
        opacity: '1 !important',
        border: `1px solid ${colors.gray[100]} !important`
      },
      '&.error': {
        border: `1px solid ${colors.red[500]} !important`
      },
      ':is(:hover, :focus)': {
        border: `1px solid ${colors.tertiary[500]}`,
        transition: 'all 150ms ease-in-out'
      }
    }
  }
};

export default { component, style };
