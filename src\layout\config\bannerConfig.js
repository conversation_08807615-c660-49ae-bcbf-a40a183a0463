import { DashboardBanner, BreadcrumbBanner } from 'common/components';
import { ROUTE_URL } from 'common/routeUrls';

/**
 * Banner Configuration System
 *
 * This configuration defines which banners should be displayed on which routes.
 * Each banner configuration includes:
 * - component: The React component to render
 * - routes: Array of route patterns that should display this banner
 * - props: Default props to pass to the component (can be overridden)
 * - priority: Display order when multiple banners match (lower = higher priority)
 */

export const BANNER_TYPES = {
  DASHBOARD: 'dashboard',
  BREADCRUMB: 'breadcrumb'
};

export const bannerConfig = [
  {
    id: BANNER_TYPES.DASHBOARD,
    component: DashboardBanner,
    routes: [
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.SCHOLAR.BASE}`,
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.SAMPLE.BASE}`,
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.APPLICANT.BASE.DASHBOARD}`,
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.APPLICANT.BASE.NEW_APPLICATION}`,
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.APPLICANT.BASE.MY_APPLICATIONS}`
    ],
    props: {
      subtitle: 'defaultSubtitle'
    },
    priority: 1
  },
  {
    id: BANNER_TYPES.BREADCRUMB,
    component: BreadcrumbBanner,
    routes: [
      `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.APPLICANT.BASE.APPLICATION}`
    ],
    props: {
      subtitle: 'defaultSubtitle'
    },
    priority: 2
  }
];
