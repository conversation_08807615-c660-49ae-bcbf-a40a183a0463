import React from 'react';
import {
  Box, HStack, Text, Circle, Flex, useColorModeValue
} from 'common/components';

const StepperStep = ({
  isActive,
  isCompleted,
  label,
  isLast
}) => {
  const activeColor = useColorModeValue('secondary.500', 'secondary.300');
  const completedColor = useColorModeValue('secondary.500', 'secondary.300');
  const inactiveColor = useColorModeValue('gray.300', 'gray.600');
  const lineColor = useColorModeValue('gray.200', 'gray.600');

  const activeLabelColor = useColorModeValue('secondary.600', 'secondary.300');
  const inactiveLabelColor = useColorModeValue('gray.500', 'gray.400');

  return (
    <Flex direction="column" align="center" flex={1} position="relative">
      <HStack spacing={0} width="100%" justify="center" position="relative">
        <Circle
          size="24px"
          bg={isActive || isCompleted ? activeColor : 'white'}
          border="2px solid"
          borderColor={isActive || isCompleted ? activeColor : inactiveColor}
          color={isActive || isCompleted ? 'white' : inactiveColor}
          fontSize="sm"
          fontWeight="bold"
          zIndex={2}
        >
          {isCompleted ? '✓' : <Box bg={isActive || isCompleted ? 'white' : inactiveColor} borderRadius="full" boxSize={2.5} />}
        </Circle>

        {!isLast && (
          <Box
            position="absolute"
            left="50%"
            top="50%"
            transform="translateY(-50%)"
            width="calc(100% - 2px)"
            marginLeft="12px"
            height="2px"
            bg={isCompleted ? completedColor : lineColor}
            zIndex={1}
          />
        )}
      </HStack>

      <Text
        mt={3}
        fontSize="sm"
        fontWeight={isActive ? 'semibold' : 'normal'}
        color={isActive ? activeLabelColor : inactiveLabelColor}
        textAlign="center"
        lineHeight="1.3"
      >
        {label}
      </Text>
    </Flex>
  );
};

const Stepper = ({
  steps = [],
  currentStep = 0,
  spacing = 4,
  ...props
}) => {
  return (
    <Box width="100%" {...props}>
      <HStack spacing={spacing} align="flex-start" justify="space-between">
        {steps.map((step, index) => (
          <StepperStep
            key={`${index + 1}-${step.label}`}
            stepNumber={index + 1}
            label={step.label || step}
            isActive={index === currentStep}
            isCompleted={index < currentStep}
            isLast={index === steps.length - 1}
          />
        ))}
      </HStack>
    </Box>
  );
};

export default Stepper;
