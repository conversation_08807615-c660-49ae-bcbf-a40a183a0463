export function CheckSvg(props) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12 21V21C7.029 21 3 16.971 3 12V12C3 7.029 7.029 3 12 3V3C16.971 3 21 7.029 21 12V12C21 16.971 16.971 21 12 21Z"
        stroke="#44A047"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 10L11 15L8 12"
        stroke="#44A047"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export const OverflowIcon = () => {
  return (
    <svg width="44" height="25" viewBox="0 0 44 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_1_4)">
        <rect x="7" y="3.5" width="30" height="10" rx="5" fill="#E8ECEE" shapeRendering="crispEdges" />
        <circle cx="13" cy="8.5" r="2" fill="#00B2EC" />
        <circle cx="22" cy="8.5" r="2" fill="#00B2EC" />
        <circle cx="31" cy="8.5" r="2" fill="#00B2EC" />
      </g>
      <defs>
        <filter id="filter0_d_1_4" x="0" y="0.5" width="44" height="24" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
          <feOffset dy="4" />
          <feGaussianBlur stdDeviation="3.5" />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0" />
          <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4" />
          <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4" result="shape" />
        </filter>
      </defs>
    </svg>
  );
};

export function InfoSvg(props) {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g clipPath="url(#clip0_1178_2647)">
        <path
          d="M10 17.5C14.1421 17.5 17.5 14.1421 17.5 10C17.5 5.85786 14.1421 2.5 10 2.5C5.85786 2.5 2.5 5.85786 2.5 10C2.5 14.1421 5.85786 17.5 10 17.5Z"
          fill="#50538A"
        />
        <path d="M10 6.6665H10.0083" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M9.1665 10H9.99984V13.3333H10.8332" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_1178_2647">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export const EditIcon = (props) => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M28 17.4263V25.2996C28 26.7929 26.7987 28.0036 25.316 28.0036H6.684C5.20133 28.0036 4 26.7929 4 25.2996V8.04092C4 6.54758 5.20133 5.33691 6.684 5.33691H16" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.4375 22.8992L15.4122 21.6552C15.6468 21.5966 15.8602 21.4752 16.0322 21.3046L27.2188 10.1179C28.2602 9.07658 28.2602 7.38858 27.2188 6.34725L26.9895 6.11791C25.9482 5.07658 24.2602 5.07658 23.2188 6.11791L12.0322 17.3046C11.8615 17.4752 11.7402 17.6899 11.6815 17.9246L10.4375 22.8992" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M21.1025 8.22363L25.1025 12.2236" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.3984 21.6598C15.417 21.5184 15.441 21.3784 15.441 21.2318C15.441 19.3904 13.949 17.8984 12.1077 17.8984C11.961 17.8984 11.821 17.9238 11.6797 17.9411" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export function UserIcon() {
  return (
    <svg width="16" height="18" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.79625 9.91699V9.16699C6.74335 9.16699 5.06325 7.48767 5.06325 5.43399H4.31325H3.56325C3.56325 8.31631 5.91514 10.667 8.79625 10.667V9.91699ZM4.31325 5.43399H5.06325C5.06325 3.37294 6.7507 1.66699 8.79625 1.66699V0.916992V0.166992C5.9078 0.166992 3.56325 2.55905 3.56325 5.43399H4.31325ZM8.79625 0.916992V1.66699C10.8491 1.66699 12.5292 3.34631 12.5292 5.39999H13.2792H14.0292C14.0292 2.51767 11.6774 0.166992 8.79625 0.166992V0.916992ZM13.2792 5.39999H12.5292C12.5292 7.46105 10.8418 9.16699 8.79625 9.16699V9.91699V10.667C11.6847 10.667 14.0292 8.27493 14.0292 5.39999H13.2792ZM15.7962 18.917V18.167H1.79625V18.917V19.667H15.7962V18.917ZM1.79625 18.917V18.167C1.66046 18.167 1.54625 18.0528 1.54625 17.917H0.796249H0.0462494C0.0462494 18.8812 0.832036 19.667 1.79625 19.667V18.917ZM0.796249 17.917H1.54625V16.917H0.796249H0.0462494V17.917H0.796249ZM0.796249 16.917H1.54625C1.54625 15.1312 3.01046 13.667 4.79625 13.667V12.917V12.167C2.18204 12.167 0.0462494 14.3028 0.0462494 16.917H0.796249ZM4.79625 12.917V13.667H12.7962V12.917V12.167H4.79625V12.917ZM12.7962 12.917V13.667C14.582 13.667 16.0462 15.1312 16.0462 16.917H16.7962H17.5462C17.5462 14.3028 15.4105 12.167 12.7962 12.167V12.917ZM16.7962 16.917H16.0462V17.917H16.7962H17.5462V16.917H16.7962ZM16.7962 17.917H16.0462C16.0462 18.0528 15.932 18.167 15.7962 18.167V18.917V19.667C16.7605 19.667 17.5462 18.8812 17.5462 17.917H16.7962Z" fill="#D4D4D4" />
    </svg>
  );
}

export function Calender() {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g clipPath="url(#clip0_1216_11113)">
        <path
          d="M18 5H6C4.89543 5 4 5.89543 4 7V19C4 20.1046 4.89543 21 6 21H18C19.1046 21 20 20.1046 20 19V7C20 5.89543 19.1046 5 18 5Z"
          stroke="#9AA6AD"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M16 3V7" stroke="#9AA6AD" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M8 3V7" stroke="#9AA6AD" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M4 11H20" stroke="#9AA6AD" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M10 15H8V17H10V15Z" stroke="#9AA6AD" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_1216_11113">
          <rect width="24" height="24" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export function Tick() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
      <path
        d="M14.0458 3.4856C14.3299 3.78703 14.3158 4.26169 14.0144 4.54579L6.08456 12.0197C5.74829 12.3366 5.22042 12.3269 4.89609 11.9977L2.21576 9.27737C1.92504 8.98231 1.92856 8.50745 2.22361 8.21674C2.51867 7.92602 2.99353 7.92954 3.28424 8.22459L5.51839 10.4921L12.9856 3.45421C13.287 3.17011 13.7617 3.18416 14.0458 3.4856Z"
        fill="#242424"
      />
    </svg>
  );
}

export function ArrowIcon(props) {
  const {
    width = '16',
    height = '18',
    color = 'white',
    direction = 'left',
    ...rest
  } = props;

  const rotation = direction === 'right' ? 'rotate(180 12 9)' : undefined;

  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <g transform={rotation}>
        <path d="M1.5 9H22.5" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M9 1.5L1.5 9" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M9 16.5L1.5 9" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </g>
    </svg>
  );
}

export function Dashboard(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <mask id="mask0_1819_1400" style={{ maskType: 'luminance' }} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="24">
        <rect width="24" height="24" fill="white" />
      </mask>
      <g mask="url(#mask0_1819_1400)">
        <path d="M10.2528 5.88415L10.5312 10.0241L10.6694 12.1049C10.6709 12.3189 10.7044 12.5315 10.7691 12.7358C10.936 13.1324 11.3376 13.3844 11.7745 13.3668L18.4317 12.9313C18.72 12.9266 18.9984 13.0344 19.2056 13.2311C19.3783 13.395 19.4898 13.6094 19.525 13.84L19.5368 13.98C19.2613 17.7947 16.4596 20.9765 12.6528 21.7978C8.84591 22.6191 4.94222 20.8841 3.06107 17.5347C2.51875 16.5616 2.18001 15.4921 2.06474 14.3888C2.01659 14.0622 1.99539 13.7323 2.00134 13.4023C1.99539 9.31254 4.90783 5.77677 8.98469 4.92438C9.47537 4.84797 9.95639 5.10773 10.1532 5.55534C10.2041 5.659 10.2377 5.77002 10.2528 5.88415Z" stroke="#50538A" />
        <path d="M22.0002 9.81229L21.9932 9.84488L21.973 9.89227L21.9758 10.0224C21.9654 10.1947 21.8988 10.3605 21.7842 10.4945C21.6647 10.634 21.5015 10.729 21.3218 10.7659L21.2122 10.7809L13.5314 11.2786C13.2759 11.3038 13.0215 11.2214 12.8316 11.052C12.6732 10.9107 12.572 10.7201 12.5434 10.5147L12.0279 2.84506C12.0189 2.81913 12.0189 2.79102 12.0279 2.76508C12.0349 2.55367 12.128 2.35384 12.2863 2.21023C12.4445 2.06662 12.6549 1.9912 12.8702 2.00082C17.4301 2.11683 21.2625 5.39579 22.0002 9.81229Z" fill="#9EA4FE" />
      </g>
    </svg>

  );
}

export function ApplicationIcon(props) {
  const { stroke = '#C9CAE0', accent = '#9EA4FE', ...rest } = props;

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
    >
      <path d="M21 13V16C21 18.7614 18.7614 21 16 21H8C5.23858 21 3 18.7614 3 16V8C3 5.23858 5.23858 3 8 3H11" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <circle cx="18" cy="6" r="4" stroke={accent} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19.25 5.5L17.6883 7L16.75 6.1" stroke={accent} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M12 12H14" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 8H11" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 12H9" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 16H12" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M15 16H17" stroke={stroke} strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function MyApplication(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M7 12C3 12 3 13.79 3 16V17C3 19.76 3 22 8 22H16C20 22 21 19.76 21 17V16C21 13.79 21 12 17 12C16 12 15.72 12.21 15.2 12.6L14.18 13.68C13 14.94 11 14.94 9.81 13.68L8.8 12.6C8.28 12.21 8 12 7 12Z" stroke="#02579A" strokeWidth="1.125" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19 12V6C19 3.79 19 2 15 2H9C5 2 5 3.79 5 6V12" stroke="#02579A" strokeWidth="1.125" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M10.5498 9.23047H13.8798H10.5498Z" fill="#A01261" />
      <path d="M10.5498 9.23047H13.8798" stroke="#5E9BCB" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9.71973 6.23047H14.7197H9.71973Z" fill="#A01261" />
      <path d="M9.71973 6.23047H14.7197" stroke="#5E9BCB" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function DraftIcon(props) {
  const {
    width = '16', height = '18', stroke = '#02579A', ...rest
  } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path fillRule="evenodd" clipRule="evenodd" d="M14.8387 1.5H3.68169C2.57169 1.5 1.67369 2.404 1.68169 3.515L1.78569 17.515C1.79369 18.614 2.68669 19.5 3.78569 19.5H17.6667C18.7717 19.5 19.6667 18.605 19.6667 17.5V6.328C19.6667 5.798 19.4557 5.289 19.0807 4.914L16.2527 2.086C15.8777 1.711 15.3697 1.5 14.8387 1.5Z" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14.668 1.5V5.409C14.668 5.961 14.22 6.409 13.668 6.409H7.66797C7.11597 6.409 6.66797 5.961 6.66797 5.409V1.5" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M5.6748 19.5V11.786C5.6748 11.076 6.2508 10.5 6.9608 10.5H14.3898C15.0988 10.5 15.6748 11.076 15.6748 11.786V19.5" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
}

export function WarningIcon() {
  return (
    <svg fill="#F59E0B" width="21px" height="21px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <path d="M12,2A10,10,0,1,0,22,12,10,10,0,0,0,12,2ZM11,7a1,1,0,0,1,2,0v6a1,1,0,0,1-2,0Zm1,12a1.5,1.5,0,1,1,1.5-1.5A1.5,1.5,0,0,1,12,19Z" />
    </svg>
  );
}

export function UserEdit(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19.2101 15.74L15.67 19.2801C15.53 19.4201 15.4 19.68 15.37 19.87L15.18 21.22C15.11 21.71 15.45 22.05 15.94 21.98L17.29 21.79C17.48 21.76 17.75 21.63 17.88 21.49L21.42 17.95C22.03 17.34 22.32 16.63 21.42 15.73C20.53 14.84 19.8201 15.13 19.2101 15.74Z" stroke="#5E9BCB" strokeWidth="1.125" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M18.7002 16.25C19.0002 17.33 19.8402 18.17 20.9202 18.47" stroke="#5E9BCB" strokeWidth="1.125" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M3.40991 22C3.40991 18.13 7.25994 15 11.9999 15C13.0399 15 14.0399 15.15 14.9699 15.43" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function Setting(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M13.7679 10.2322C14.7442 11.2085 14.7442 12.7915 13.7679 13.7678C12.7916 14.7441 11.2087 14.7441 10.2324 13.7678C9.25604 12.7915 9.25604 11.2085 10.2324 10.2322C11.2087 9.25592 12.7916 9.25592 13.7679 10.2322" stroke="#5E9BCB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M14.849 4.1202L15.432 4.3142C15.966 4.4922 16.327 4.9922 16.327 5.5552V6.3922C16.327 7.1042 16.895 7.6852 17.607 7.7002L18.445 7.7182C18.93 7.7282 19.37 8.0072 19.587 8.4412L19.862 8.9912C20.114 9.4952 20.015 10.1032 19.617 10.5012L19.025 11.0932C18.522 11.5962 18.513 12.4092 19.005 12.9232L19.585 13.5292C19.921 13.8802 20.035 14.3872 19.881 14.8482L19.687 15.4312C19.509 15.9652 19.009 16.3262 18.446 16.3262H17.609C16.897 16.3262 16.316 16.8942 16.301 17.6062L16.283 18.4442C16.273 18.9292 15.994 19.3692 15.56 19.5862L15.01 19.8612C14.506 20.1132 13.898 20.0142 13.5 19.6162L12.908 19.0242C12.405 18.5212 11.592 18.5122 11.078 19.0042L10.472 19.5842C10.121 19.9202 9.61403 20.0342 9.15303 19.8802L8.57003 19.6862C8.03603 19.5082 7.67503 19.0082 7.67503 18.4452V17.6082C7.67503 16.8962 7.10703 16.3152 6.39503 16.3002L5.55703 16.2822C5.07203 16.2722 4.63203 15.9932 4.41503 15.5592L4.14003 15.0092C3.88803 14.5052 3.98703 13.8972 4.38503 13.4992L4.97703 12.9072C5.48003 12.4042 5.48903 11.5912 4.99703 11.0772L4.41703 10.4712C4.08003 10.1192 3.96603 9.6112 4.12003 9.1512L4.31403 8.5682C4.49203 8.0342 4.99203 7.6732 5.55503 7.6732H6.39203C7.10403 7.6732 7.68503 7.1052 7.70003 6.3932L7.71803 5.5552C7.73003 5.0702 8.00803 4.6302 8.44203 4.4132L8.99203 4.1382C9.49603 3.8862 10.104 3.9852 10.502 4.3832L11.094 4.9752C11.597 5.4782 12.41 5.4872 12.924 4.9952L13.53 4.4152C13.881 4.0802 14.389 3.9662 14.849 4.1202V4.1202Z" stroke="#02579A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function BellIcon(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M21.6732 18.5536C21.0303 17.9805 20.4675 17.3235 19.9999 16.6003C19.4894 15.602 19.1834 14.5117 19.0999 13.3936V10.1003C19.1043 8.344 18.4672 6.64658 17.3084 5.32691C16.1495 4.00724 14.5486 3.15617 12.8065 2.93359V2.07359C12.8065 1.83755 12.7128 1.61118 12.5459 1.44427C12.379 1.27736 12.1526 1.18359 11.9165 1.18359C11.6805 1.18359 11.4541 1.27736 11.2872 1.44427C11.1203 1.61118 11.0265 1.83755 11.0265 2.07359V2.94693C9.30004 3.18555 7.71852 4.04176 6.57489 5.357C5.43126 6.67223 4.80302 8.35736 4.80654 10.1003V13.3936C4.72304 14.5117 4.41705 15.602 3.90654 16.6003C3.44712 17.3218 2.89333 17.9788 2.25987 18.5536C2.18876 18.6161 2.13176 18.693 2.09268 18.7792C2.0536 18.8654 2.03332 18.9589 2.0332 19.0536V19.9603C2.0332 20.1371 2.10344 20.3066 2.22847 20.4317C2.35349 20.5567 2.52306 20.6269 2.69987 20.6269H21.2332C21.41 20.6269 21.5796 20.5567 21.7046 20.4317C21.8296 20.3066 21.8999 20.1371 21.8999 19.9603V19.0536C21.8997 18.9589 21.8795 18.8654 21.8404 18.7792C21.8013 18.693 21.7443 18.6161 21.6732 18.5536ZM3.41987 19.2936C4.04014 18.6944 4.58627 18.0229 5.04654 17.2936C5.68961 16.0879 6.06482 14.7576 6.14654 13.3936V10.1003C6.1201 9.31895 6.25115 8.54031 6.5319 7.81071C6.81265 7.0811 7.23734 6.41545 7.7807 5.85339C8.32406 5.29134 8.97496 4.84437 9.69466 4.53911C10.4144 4.23385 11.1881 4.07653 11.9699 4.07653C12.7516 4.07653 13.5254 4.23385 14.2451 4.53911C14.9648 4.84437 15.6157 5.29134 16.159 5.85339C16.7024 6.41545 17.1271 7.0811 17.4078 7.81071C17.6886 8.54031 17.8196 9.31895 17.7932 10.1003V13.3936C17.8749 14.7576 18.2501 16.0879 18.8932 17.2936C19.3535 18.0229 19.8996 18.6944 20.5199 19.2936H3.41987Z" fill="#022F21" />
      <path d="M11.9996 22.853C12.4195 22.8433 12.8225 22.6855 13.1373 22.4073C13.4521 22.1291 13.6583 21.7486 13.7196 21.333H10.2129C10.2759 21.7599 10.4918 22.1493 10.8204 22.4289C11.1491 22.7085 11.5681 22.8592 11.9996 22.853Z" fill="#022F21" />
    </svg>
  );
}

export function NewApplication(props) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M21 13V16C21 18.7614 18.7614 21 16 21H8C5.23858 21 3 18.7614 3 16V8C3 5.23858 5.23858 3 8 3H11" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <circle cx="18" cy="6" r="4" stroke="#5E9BCB" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19.25 5.5L17.6883 7L16.75 6.1" stroke="#5E9BCB" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M12 12H14" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 8H11" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 12H9" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M7 16H12" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M15 16H17" stroke="#02579A" strokeWidth="1.125" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function SearchIcon(props) {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="M27.414 24.586L22.337 19.509C23.386 17.928 24 16.035 24 14C24 8.486 19.514 4 14 4C8.486 4 4 8.486 4 14C4 19.514 8.486 24 14 24C16.035 24 17.928 23.386 19.509 22.337L24.586 27.414C25.366 28.195 26.634 28.195 27.414 27.414C28.195 26.633 28.195 25.367 27.414 24.586ZM7 14C7 10.14 10.14 7 14 7C17.86 7 21 10.14 21 14C21 17.86 17.86 21 14 21C10.14 21 7 17.86 7 14Z"
        fill="#D0D0D0"
      />
    </svg>
  );
}

export function DownArrow(props) {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path d="M4 6L8 10L12 6" stroke="#151D48" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

export function Hamburger(props) {
  return (
    <svg viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g data-name="menu " id="menu_">
        <path d="M29,6H3A1,1,0,0,0,3,8H29a1,1,0,0,0,0-2Z" />
        <path d="M3,17H16a1,1,0,0,0,0-2H3a1,1,0,0,0,0,2Z" />
        <path d="M25,24H3a1,1,0,0,0,0,2H25a1,1,0,0,0,0-2Z" />
      </g>
    </svg>
  );
}
export const ChevronDownIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M6 9L12 15L18 9"
      stroke="#4B5563"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InfoIcon = () => (
  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M10 19C14.9706 19 19 14.9706 19 10C19 5.02944 14.9706 1 10 1C5.02944 1 1 5.02944 1 10C1 14.9706 5.02944 19 10 19Z" stroke="#E42E78" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 6H10.01" stroke="#E42E78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9 10H10V14H11" stroke="#E42E78" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const RpGroupIcon = () => (
  <svg width="185" height="44" viewBox="0 0 185 44" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.681183" y="0.21582" width="184" height="43" fill="url(#pattern0)" />
    <defs>
      <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
        <use href="#image0" transform="scale(0.00543478 0.0232558)" />
      </pattern>
      <image
        id="image0"
        width="184"
        height="43"
        preserveAspectRatio="none"
        href="data:image/png;base64,..."
      />
    </defs>
  </svg>
);

export const BackForward = () => {
  return (
    <svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M32.6962 19.9831H7.7295" stroke="#9AA6AD" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M17.7012 29.9609L7.74119 19.9993L17.7012 10.0376" stroke="#9AA6AD" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const ChevronLeftIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M15 18L9 12L15 6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChevronRightIcon = (props) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M9 18L15 12L9 6"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ExpandIcon = (props) => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M17.6602 3H21.6602V7" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.6602 9L21.6602 3" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.66016 21H3.66016V17" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.66016 15L3.66016 21" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M4.66016 11V6C4.66016 4.895 5.55516 4 6.66016 4H11.6602" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.6602 13V18C20.6602 19.105 19.7652 20 18.6602 20H12.6602" stroke="#50538A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const PDFIcon = (props) => (
  <svg width="39" height="38" viewBox="0 0 39 38" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M14.5488 1.58203H25.8809L37.4902 13.1914V32.1699C37.4902 34.5162 35.5884 36.4189 33.2422 36.4189H14.5488C12.2027 36.4187 10.3008 34.5161 10.3008 32.1699V5.83008C10.301 3.48408 12.2028 1.58224 14.5488 1.58203Z" fill="#EEF1F7" stroke="#CBD0DC" strokeWidth="1.69935" />
    <rect x="0.529297" y="16.876" width="25.4902" height="14.8693" rx="2.97386" fill="#D82042" />
    <path d="M4.75391 27.9218V20.8154H7.28693C7.83981 20.8154 8.29783 20.9161 8.66102 21.1173C9.0242 21.3186 9.29601 21.5938 9.47644 21.9431C9.65688 22.2901 9.74709 22.6811 9.74709 23.116C9.74709 23.5532 9.65572 23.9464 9.47297 24.2957C9.29254 24.6427 9.01957 24.918 8.65408 25.1216C8.29089 25.3228 7.83402 25.4235 7.28346 25.4235H5.54157V24.5143H7.18631C7.53561 24.5143 7.81899 24.4542 8.03643 24.3339C8.25388 24.2113 8.4135 24.0448 8.51528 23.8342C8.61706 23.6237 8.66796 23.3843 8.66796 23.116C8.66796 22.8476 8.61706 22.6094 8.51528 22.4012C8.4135 22.193 8.25272 22.0299 8.03296 21.9119C7.81552 21.7939 7.52867 21.735 7.17243 21.735H5.82611V27.9218H4.75391Z" fill="white" />
    <path d="M13.3046 27.9218H11.0041V20.8154H13.3775C14.0738 20.8154 14.6718 20.9577 15.1714 21.2422C15.6711 21.5244 16.0539 21.9304 16.32 22.4602C16.5883 22.9876 16.7225 23.6203 16.7225 24.3582C16.7225 25.0984 16.5871 25.7346 16.3165 26.2666C16.0481 26.7987 15.6595 27.2081 15.1506 27.495C14.6417 27.7795 14.0264 27.9218 13.3046 27.9218ZM12.0763 26.9849H13.2456C13.7869 26.9849 14.2369 26.8831 14.5954 26.6796C14.954 26.4737 15.2223 26.1764 15.4004 25.7878C15.5786 25.3969 15.6676 24.9203 15.6676 24.3582C15.6676 23.8007 15.5786 23.3276 15.4004 22.939C15.2246 22.5504 14.9621 22.2554 14.6128 22.0542C14.2635 21.8529 13.8297 21.7523 13.3116 21.7523H12.0763V26.9849Z" fill="white" />
    <path d="M18.0558 27.9218V20.8154H22.4625V21.7384H19.128V23.9036H22.1468V24.8232H19.128V27.9218H18.0558Z" fill="white" />
  </svg>

);

export const SaveArrowIcon = (props) => {
  const {
    width = '21', height = '21', color = 'white', ...rest
  } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path fillRule="evenodd" clipRule="evenodd" d="M17.333 27.1663L26.6663 5.83301L5.33301 15.1663L15.2957 17.1903L17.333 27.1663Z" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const CloseIcon = (props) => {
  const {
    width = '24', height = '24', color = '#717680', ...rest
  } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path d="M18 6L6 18M6 6L18 18" stroke={color} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export const DoneIcon = (props) => {
  const {
    width = '30', height = '30', ...rest
  } = props;
  return (

    <svg width={width} height={height} viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path
        d="M14.4783 0.310547C6.80799 0.310547 0.557617 6.56092 0.557617 14.2312C0.557617 21.9015 6.80799 28.1519 14.4783 28.1519C22.1486 28.1519 28.3989 21.9015 28.3989 14.2312C28.3989 6.56092 22.1486 0.310547 14.4783 0.310547ZM21.1324 11.0295L13.2393 18.9225C13.0445 19.1174 12.78 19.2287 12.5015 19.2287C12.2231 19.2287 11.9586 19.1174 11.7638 18.9225L7.8242 14.9829C7.4205 14.5792 7.4205 13.911 7.8242 13.5073C8.2279 13.1036 8.89609 13.1036 9.29979 13.5073L12.5015 16.7091L19.6568 9.55387C20.0605 9.15017 20.7287 9.15017 21.1324 9.55387C21.5361 9.95757 21.5361 10.6118 21.1324 11.0295Z"
        fill="#23A26D"
      />
    </svg>

  );
};

export const ErrorIcon = (props) => {
  const {
    width = '22', height = '22', ...rest
  } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 17 17" {...rest}>
      <path fill="currentColor" d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z" />
      <path
        fill="currentColor"
        d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"
      />
    </svg>
  );
};

export const AlertWarningIcon = (props) => {
  const {
    width = '22', height = '22', ...rest
  } = props;
  return (
    <svg width={width} height={height} viewBox="0 0 17 17" {...rest}>
      <path
        fill="currentColor"
        d="M7.938 2.016A.13.13 0 0 1 8.002 2a.13.13 0 0 1 .063.016.146.146 0 0 1 .054.057l6.857 11.667c.036.06.035.124.002.183a.163.163 0 0 1-.054.06.116.116 0 0 1-.066.017H1.146a.115.115 0 0 1-.066-.017.163.163 0 0 1-.054-.06.176.176 0 0 1 .002-.183L7.884 2.073a.147.147 0 0 1 .054-.057zm1.044-.45a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566z"
      />
      <path
        fill="currentColor"
        d="M7.002 12a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 5.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995z"
      />
    </svg>
  );
};

export const UploadIcon = (props) => (
  <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M21.8916 15.5V19.5C21.8916 20.0304 21.6809 20.5391 21.3058 20.9142C20.9307 21.2893 20.422 21.5 19.8916 21.5H5.8916C5.36117 21.5 4.85246 21.2893 4.47739 20.9142C4.10232 20.5391 3.8916 20.0304 3.8916 19.5V15.5" stroke="#425166" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M17.8916 8.5L12.8916 3.5L7.8916 8.5" stroke="#425166" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.8916 3.5V15.5" stroke="#425166" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export const GuideLines = (props) => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <rect width="32" height="32" rx="16" fill="#F59E0B" />
    <path d="M13.9374 19.5002C13.8482 19.1542 13.6678 18.8384 13.415 18.5856C13.1623 18.3329 12.8465 18.1525 12.5004 18.0632L6.36543 16.4812C6.26076 16.4515 6.16864 16.3885 6.10304 16.3017C6.03744 16.2149 6.00195 16.1091 6.00195 16.0002C6.00195 15.8914 6.03744 15.7856 6.10304 15.6988C6.16864 15.612 6.26076 15.549 6.36543 15.5192L12.5004 13.9362C12.8464 13.8471 13.1621 13.6668 13.4148 13.4143C13.6675 13.1618 13.848 12.8461 13.9374 12.5002L15.5194 6.36525C15.5488 6.26017 15.6118 6.16759 15.6987 6.10164C15.7857 6.0357 15.8918 6 16.0009 6C16.11 6 16.2162 6.0357 16.3031 6.10164C16.39 6.16759 16.453 6.26017 16.4824 6.36525L18.0634 12.5002C18.1527 12.8463 18.3331 13.1621 18.5858 13.4149C18.8385 13.6676 19.1544 13.848 19.5004 13.9372L25.6354 15.5182C25.7409 15.5473 25.834 15.6103 25.9003 15.6973C25.9666 15.7844 26.0025 15.8908 26.0025 16.0002C26.0025 16.1097 25.9666 16.2161 25.9003 16.3032C25.834 16.3902 25.7409 16.4531 25.6354 16.4822L19.5004 18.0632C19.1544 18.1525 18.8385 18.3329 18.5858 18.5856C18.3331 18.8384 18.1527 19.1542 18.0634 19.5002L16.4814 25.6353C16.452 25.7403 16.389 25.8329 16.3021 25.8989C16.2152 25.9648 16.109 26.0005 15.9999 26.0005C15.8908 26.0005 15.7847 25.9648 15.6977 25.8989C15.6108 25.8329 15.5478 25.7403 15.5184 25.6353L13.9374 19.5002Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M24 7V11" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M26 9H22" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 21V23" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9 22H7" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export const TrashIcon = ({ stroke = '#323232', ...props }) => (
  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path d="M18.6602 6V18.75C18.6602 19.993 17.6332 21 16.3912 21H8.89116C7.64816 21 6.66016 19.993 6.66016 18.75V6" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.1602 6H5.16016" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6602 3H14.6602" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.6602 10V17" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10.6602 17V10" stroke={stroke} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

export const SuccessIcon = (props) => (
  <svg width="121" height="121" viewBox="0 0 121 121" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <circle cx="60.2128" cy="60.3281" r="60" fill="#23A26D" fillOpacity="0.12" />
    <path d="M59.1418 31.7559C43.399 31.7559 30.5704 44.5844 30.5704 60.3273C30.5704 76.0701 43.399 88.8987 59.1418 88.8987C74.8847 88.8987 87.7133 76.0701 87.7133 60.3273C87.7133 44.5844 74.8847 31.7559 59.1418 31.7559ZM72.799 53.7559L56.599 69.9559C56.199 70.3559 55.6561 70.5844 55.0847 70.5844C54.5133 70.5844 53.9704 70.3559 53.5704 69.9559L45.4847 61.8701C44.6561 61.0416 44.6561 59.6701 45.4847 58.8416C46.3133 58.013 47.6847 58.013 48.5133 58.8416L55.0847 65.413L69.7704 50.7273C70.599 49.8987 71.9704 49.8987 72.799 50.7273C73.6275 51.5559 73.6275 52.8987 72.799 53.7559Z" fill="#23A26D" />
  </svg>

);

export const FilterIcon = (props) => (
  <svg width="10" height="10" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
    <path
      d="M2.92513 1.13867H10.0751C10.671 1.13867 11.1585 1.62617 11.1585 2.22201V3.41367C11.1585 3.84701 10.8876 4.38867 10.6168 4.65951L8.28763 6.71784C7.96263 6.98867 7.74596 7.53034 7.74596 7.96367V10.2928C7.74596 10.6178 7.5293 11.0512 7.25846 11.2137L6.50013 11.7012C5.79596 12.1345 4.82096 11.647 4.82096 10.7803V7.90951C4.82096 7.53034 4.6043 7.04284 4.38763 6.77201L2.3293 4.60534C2.05846 4.33451 1.8418 3.84701 1.8418 3.52201V2.27617C1.8418 1.62617 2.3293 1.13867 2.92513 1.13867Z"
      stroke="#D0D0D0"
      strokeWidth="1.2"
      strokeMiterlimit="8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Refresh = (props) => {
  const { size = 16, color = '#323232', ...rest } = props;
  return (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path
        d="M1 4V10H7M23 20V14H17M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15"
        stroke={color}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const Verified = (props) => {
  const { size = 16, color = '#23A26D', ...rest } = props;
  return (

    <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" {...rest}>
      <g
        fill="none"
        stroke={color}
        strokeWidth="1.5"
      >
        <path strokeLinecap="round" strokeLinejoin="round" d="M2 20v-1a7 7 0 0 1 7-7v0" /><path d="M15.804 12.314a1.62 1.62 0 0 1 2.392 0c.325.356.79.549 1.272.526a1.62 1.62 0 0 1 1.692 1.692c-.023.481.17.947.526 1.272c.705.642.705 1.75 0 2.392c-.356.325-.549.79-.526 1.272a1.62 1.62 0 0 1-1.692 1.692a1.62 1.62 0 0 0-1.272.526a1.62 1.62 0 0 1-2.392 0a1.62 1.62 0 0 0-1.272-.526a1.62 1.62 0 0 1-1.692-1.692a1.62 1.62 0 0 0-.527-1.272a1.62 1.62 0 0 1 0-2.392c.357-.325.55-.79.527-1.272a1.62 1.62 0 0 1 1.692-1.692c.481.023.947-.17 1.272-.527Z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="m15.364 17l1.09 1.09l2.182-2.18M9 12a4 4 0 1 0 0-8a4 4 0 0 0 0 8" />
      </g>
    </svg>
  );
};

export const Camera = (props) => {
  const { size = 32, color = '#BDBDBD', ...rest } = props;
  return (
    <svg width={size} height={size} viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest}>
      <path d="M19.8387 14.057C21.9147 16.133 21.9147 19.499 19.8387 21.575C17.7626 23.651 14.3967 23.651 12.3207 21.575C10.2447 19.499 10.2447 16.133 12.3207 14.057C14.3967 11.981 17.7626 11.981 19.8387 14.057" stroke={color} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M28 12.5007V24.5007C28 25.974 26.8067 27.1673 25.3333 27.1673H6.66667C5.19333 27.1673 4 25.974 4 24.5007V12.5007C4 11.0273 5.19333 9.83398 6.66667 9.83398H9.33333L11.2827 6.49532C11.5213 6.08598 11.96 5.83398 12.4347 5.83398H19.5067C19.9747 5.83398 20.408 6.07932 20.6493 6.47932L22.6667 9.83398H25.3333C26.8067 9.83398 28 11.0273 28 12.5007Z" stroke="#BDBDBD" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};
