import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import resources from './locale';

const namespaces = ['template', 'common', 'validate'];

i18next
  .use(initReactI18next)
  .init(
    {
      resources,
      lng: localStorage.getItem('lang') || 'en',
      fallbackLng: 'en',
      debug: true,
      ns: namespaces,
      defaultNS: 'common',
      fallbackNS: namespaces,
      interpolation: {
        escapeValue: false
      }
    }
  );

export default i18next;
