import { Box, Text } from 'common/components';

const TitledCard = ({ title, children }) => {
  return (
    <Box
      borderRadius="md"
      boxShadow="lg"
      bg="white"
      w="100%"
    >
      <Box bg="primary.80" px={{ base: 4, md: 6 }} py={{ base: 3, md: 4 }}>
        <Text
          fontSize={{ base: 'lg', md: 'xl' }}
          fontWeight="semibold"
          color="primary.500"
        >
          {title}
        </Text>
      </Box>
      <Box px={{ base: 4, md: 6 }} py={{ base: 3, md: 4 }}>
        {children}
      </Box>
    </Box>
  );
};

export default TitledCard;
