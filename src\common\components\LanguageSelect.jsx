import {
  <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>u<PERSON><PERSON>, But<PERSON>
} from 'common/components';
import { DownArrow } from 'assets/svg';

function LanguageSelector({ selected = 'EN', data = [], handleSelect = () => {} }) {
  return (
    <Menu>
      <MenuButton
        as={Button}
        variant="ghost"
        fontWeight="medium"
        fontSize="sm"
        color="gray.700"
        rightIcon={<DownArrow />}
        _hover={{ color: 'gray.800' }}
        _active={{ bg: 'transparent' }}
        _focus={{ boxShadow: 'none' }}
        px={2}
        h="auto"
        minH="auto"
      >
        {selected}
      </MenuButton>
      <MenuList minW="80px" w="auto" bg="white" zIndex={4}>
        {data?.map((item) => (
          <MenuItem
            color="black"
            _hover={{ bg: 'gray.100', color: 'black' }}
            key={item.code}
            onClick={() => handleSelect(item)}
          >{item.name}
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
}

export default LanguageSelector;
