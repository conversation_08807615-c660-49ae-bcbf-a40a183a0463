export const STATE_REDUCER_KEY = {
  SLICE: 'common',
  API: 'common-api'
};

export const SCHOLARSHIP_TYPE = {
  HSS: 'higherSecondary',
  UG: 'underGraduate',
  PG: 'masterGraduation'
};

export const REQUEST_METHOD = {
  GET: 'GET',
  PUT: 'PUT',
  POST: 'POST',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
  MULTIPART: 'MULTIPART'
};

export const YES_OR_NO = {
  YES: 'yes',
  NO: 'no'
};

export const YES_NO_OPTIONS = [
  { code: YES_OR_NO.YES, name: 'Yes' },
  { code: YES_OR_NO.NO, name: 'No' }
];

export const APPLICATION_STATUS = {
  DRAFT: 'DRAFT',
  APPROVED: 'APPROVED',
  APPLIED: 'APPLIED',
  SUBMITTED: 'SUBMITTED',
  REJECTED: 'REJECTED'
};

export const APPLICATION_STATUS_OPTIONS = [
  { code: APPLICATION_STATUS.DRAFT, name: 'Draft' },
  { code: APPLICATION_STATUS.APPROVED, name: 'Approved' },
  { code: APPLICATION_STATUS.APPLIED, name: 'Applied' },
  { code: APPLICATION_STATUS.SUBMITTED, name: 'Submitted' },
  { code: APPLICATION_STATUS.REJECTED, name: 'Rejected' }
];

export const languages = [
  { code: 'en', name: 'EN' },
  { code: 'ml', name: 'ML' },
  { code: 'hi', name: 'HI' }
];

export const FILE_SIZE = 2024000;
export const FILE_ACCEPT_TYPE = 'image/*,application/pdf';

export const VALID_FILE_TYPE = [
  'application/pdf', 'image/jpeg', 'image/png', 'image/jpg', 'image/bmp'
];

export const USER_APPLICANT = { id: 1, code: 'APPLICANT', name: 'Applicant' };
export const USER_OFFICIAL = { id: 2, code: 'OFFICIAL', name: 'Official' };
export const SIGN_UP_USER_TYPE = [USER_APPLICANT, USER_OFFICIAL];
