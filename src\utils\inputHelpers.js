import { AADHAAR } from 'common/regex';

/**
 * Utility function to limit the number of digits in a number input field
 * @param {number} maxLength - Maximum number of digits allowed
 * @returns {function} - Event handler function for onInput
 */
export const limitInputLength = (maxLength) => {
  return (e) => {
    if (e.target.value.length > maxLength) {
      e.target.value = e.target.value.slice(0, maxLength);
    }
  };
};

/**
 * Utility function to limit input length with custom validation
 * @param {number} maxLength - Maximum number of digits allowed
 * @param {function} customValidator - Optional custom validation function
 * @returns {function} - Event handler function for onInput
 */
export const limitInputLengthWithValidation = (maxLength, customValidator) => {
  return (e) => {
    // Apply length restriction
    if (e.target.value.length > maxLength) {
      e.target.value = e.target.value.slice(0, maxLength);
    }

    // Apply custom validation if provided
    if (customValidator && typeof customValidator === 'function') {
      customValidator(e);
    }
  };
};

/**
 * Aadhaar input handler: Enforces maxLength 12 and Aadhaar format
 * Uses exact Aadhaar regex after 12 digits
 * @param {React.ChangeEvent<HTMLInputElement>} e
 */
export const aadhaarInputHandler = (e) => {
  let value = e.target.value.replace(/\D/g, '');
  value = value.slice(0, 12);
  if (value.length >= 1 && !/^[2-9]/.test(value)) {
    value = '';
  }
  if (value.length === 12 && !AADHAAR.test(value)) {
    value = '';
  }
  e.target.value = value;
};

/**
 * Predefined input handlers for common field types
 */
export const inputHandlers = {
  aadhaar: aadhaarInputHandler,
  mobile: limitInputLength(10),
  pincode: limitInputLength(6),
  percentage: limitInputLength(3),

  // Custom handlers with additional validation
  mobileWithValidation: limitInputLengthWithValidation(10, (e) => {
    // Ensure mobile number starts with 6-9
    const { value } = e.target;
    if (value.length > 0 && !['6', '7', '8', '9'].includes(value[0])) {
      e.target.value = '';
    }
  }),

  percentageWithRange: limitInputLengthWithValidation(3, (e) => {
    // Ensure percentage is between 0-100
    const value = parseInt(e.target.value, 10);
    if (value > 100) {
      e.target.value = '100';
    }
  })
};
