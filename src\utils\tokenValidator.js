import { isTokenValid, logout } from './auth';

let tokenCheckInterval = null;

export const startTokenValidation = () => {
  if (tokenCheckInterval) {
    clearInterval(tokenCheckInterval);
  }

  tokenCheckInterval = setInterval(() => {
    const isAuthRoute = window.location.pathname.includes('/ui/auth/');

    if (!isAuthRoute && !isTokenValid()) {
      logout(true);
    }
  }, 5 * 60 * 1000); // 5 minutes
};

export const stopTokenValidation = () => {
  if (tokenCheckInterval) {
    clearInterval(tokenCheckInterval);
    tokenCheckInterval = null;
  }
};

export const checkTokenNow = () => {
  const isAuthRoute = window.location.pathname.includes('/ui/auth/');

  if (!isAuthRoute && !isTokenValid()) {
    logout(true);
    return false;
  }
  return true;
};
