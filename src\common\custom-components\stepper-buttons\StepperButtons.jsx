import React from 'react';
import {
  Box,
  Button,
  Grid,
  GridItem,
  IconButton,
  IconButtonWithLabel,
  Stack
} from 'common/components';
import { ArrowIcon, DraftIcon, SaveArrowIcon } from 'assets/svg';
import { t } from 'i18next';
import colors from 'theme/foundations/colors';

const StepperButtons = ({
  // Navigation props
  currentStep = 0,
  totalSteps = 1,
  onNext,
  onPrevious,
  layout = 'space-between', // 'space-between' | 'right-aligned'
  showSaveDraft = false, // Button customization
  nextButtonProps = {}, // Button props for Next/Submit button
  submitButtonProps = {},
  saveDraftButtonProps = {}, // Button props for Save Draft button
  previousButtonProps = {}, // Previous button props
  ...rest
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === totalSteps - 1;

  // Get button props with defaults
  const currentButtonProps = isLastStep ? submitButtonProps : nextButtonProps;

  // Determine button labels (from props or defaults)
  const getNextButtonLabel = () => {
    if (isLastStep) {
      return currentButtonProps.label || submitButtonProps.label || t('submit');
    }
    return currentButtonProps.label || nextButtonProps.label || t('next');
  };

  const getNextButtonIcon = () => {
    if (currentButtonProps.icon) return currentButtonProps.icon;
    if (isLastStep) {
      return <SaveArrowIcon />;
    }
    return <ArrowIcon direction="right" color="white" />;
  };

  const getMobileSubmitLabel = () => {
    if (currentButtonProps.label) return currentButtonProps.label;
    return isLastStep ? t('submit') : t('next');
  };

  // Previous Button JSX
  const previousButtonJSX = !isFirstStep && onPrevious ? (
    <Box>
      {/* Mobile: Icon only */}
      <IconButton
        icon={previousButtonProps.icon || <ArrowIcon color={colors.primary[700]} />}
        aria-label={previousButtonProps.label || t('previous')}
        variant="ghost"
        onClick={onPrevious}
        size="md"
        display={{ base: 'flex', md: 'none' }}
        bg="transparent"
        _hover={{ bg: 'gray.50' }}
        borderRadius="md"
        minW="44px"
        h="44px"
        {...previousButtonProps}
      />

      {/* Desktop: Clean minimal design matching Figma */}
      <Button
        leftIcon={previousButtonProps.icon || <ArrowIcon color={colors.primary[700]} />}
        onClick={onPrevious}
        display={{ base: 'none', md: 'flex' }}
        variant="ghost"
        size="md"
        color={colors.primary[700]}
        fontWeight="medium"
        fontSize="18px"
        bg="transparent"
        _hover={{ bg: 'gray.50' }}
        _active={{ bg: 'gray.100' }}
        h="44px"
        minH="44px"
        borderRadius="md"
        {...previousButtonProps}
      >
        {previousButtonProps.label || t('previous')}
      </Button>
    </Box>
  ) : null;

  // Action Buttons JSX
  const actionButtonsJSX = (
    <Box>
      <Stack
        direction="row"
        spacing={{ base: 2, md: 3 }}
        align="center"
      >
        {/* Save Draft Button */}
        {showSaveDraft && (
          <IconButtonWithLabel
            label={saveDraftButtonProps.label || t('save')}
            icon={saveDraftButtonProps.icon || <DraftIcon stroke={colors.secondary[500]} />}
            variant="secondary_outline"
            iconPosition="right"
            size={{ base: 'md', md: 'lg' }}
            minW={{ base: '44px', md: 'auto' }}
            h={{ base: '44px', md: 'auto' }}
            {...saveDraftButtonProps}
          />
        )}

        {/* Next/Submit Button - Mobile */}
        <IconButtonWithLabel
          label={getMobileSubmitLabel()}
          icon={getNextButtonIcon()}
          variant="secondary"
          iconPosition="right"
          type="submit"
          size={{ base: 'md', md: 'lg' }}
          display={{ base: 'flex', md: 'none' }}
          minW="44px"
          h="44px"
          onClick={onNext}
          {...(isLastStep ? submitButtonProps : nextButtonProps)}
        />

        {/* Next/Submit Button - Desktop */}
        <IconButtonWithLabel
          label={getNextButtonLabel()}
          icon={getNextButtonIcon()}
          variant="secondary"
          iconPosition="right"
          type="submit"
          size={{ base: 'md', md: 'lg' }}
          display={{ base: 'none', md: 'flex' }}
          onClick={onNext}
          {...(isLastStep ? submitButtonProps : nextButtonProps)}
        />
      </Stack>
    </Box>
  );

  // Layout: Space Between (Previous on left, Actions on right)
  if (layout === 'space-between') {
    return (
      <Stack
        direction="row"
        justify="space-between"
        align="center"
        w="100%"
        spacing={{ base: 2, md: 0 }}
        {...rest}
      >
        {previousButtonJSX}
        {actionButtonsJSX}
      </Stack>
    );
  }

  // Layout: Right Aligned (Grid layout for first step)
  return (
    <Grid templateColumns="repeat(12, 1fr)" gap={{ base: 2, md: 4 }} {...rest}>
      <GridItem colSpan={12} textAlign={{ base: 'center', md: 'right' }}>
        <Stack
          direction="row"
          spacing={{ base: 2, md: 4 }}
          justify="flex-end"
          align="center"
          w="100%"
          flexWrap="wrap"
        >
          {actionButtonsJSX}
        </Stack>
      </GridItem>
    </Grid>
  );
};

export default StepperButtons;
