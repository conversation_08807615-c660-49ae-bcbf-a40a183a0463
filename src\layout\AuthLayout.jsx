import {
  Box,
  Card,
  useColorModeValue
} from 'common/components';
import { Outlet } from 'react-router-dom';
import HeaderLog from './components/HeaderLog';

const Layout = () => {
  const cardBg = useColorModeValue('white', 'gray.800');
  return (
    <Box
      flex="1"
      w="full"
      display="flex"
      alignItems="center"
      px={{ base: 4, md: 0 }}
    >

      <Box
        as={Card}
        display={{ base: 'block', md: 'flex' }}
        bg={{ base: 'transparent', md: cardBg }}
        shadow={{ base: 'none', md: 'lg' }}
        borderRadius={{ base: 'none', md: 'lg' }}
        w="full"
        h="full"
      >
        <Box display={{ base: 'block', md: 'none' }}>
          <HeaderLog />

        </Box>

        <Outlet />
      </Box>
    </Box>
  );
};

export default Layout;
