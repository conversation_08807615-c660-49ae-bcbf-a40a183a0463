import {
  Box, VStack, Flex, Text, Image, IconButton
} from 'common/components';
import {
  ChevronLeftIcon, ChevronRightIcon
} from 'assets/svg';
import { useState, useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { actions as commonActions } from 'pages/common/slice';
import {
  SidebarCard, KeralaLogo, RootsLogo
} from 'assets/images';
import HeaderActions from './HeaderActions';
import { NAV_ITEMS } from '../../common/navigations';

function SideBar({
  isDrawer = false, onClose, onWidthChange
}) {
  const dispatch = useDispatch();
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(true);

  const getActiveMenuItem = () => {
    const currentPath = location.pathname;
    const activeItem = NAV_ITEMS.find((item) => {
      if (!item.path) return false;

      // Check main path
      const fullPath = `/ui/${item.path}`;
      const isMainPathActive = currentPath === fullPath || currentPath.startsWith(`${fullPath}/`);

      // Check related paths if configured
      const isRelatedPathActive = item.relatedPaths?.some((relatedPath) => {
        const relatedFullPath = `/ui/${relatedPath}`;
        return currentPath === relatedFullPath || currentPath.startsWith(`${relatedFullPath}/`);
      });

      return isMainPathActive || isRelatedPathActive;
    });
    return activeItem ? activeItem.label : 'Dashboard';
  };

  const handleClick = (path) => {
    if (path) {
      dispatch(commonActions.navigateTo({
        to: `/ui/${path}`
      }));
    }

    if (isDrawer && onClose) onClose(); // auto-close drawer on mobile
  };

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  const getWidth = () => {
    if (isDrawer) return '100%';
    if (isExpanded) return '250px';
    return '80px';
  };

  useEffect(() => {
    if (!isDrawer && onWidthChange) {
      onWidthChange(getWidth());
    }
  }, [isExpanded, isDrawer, onWidthChange]);

  return (
    <Box
      bg="white"
      color="gray.800"
      w={getWidth()}
      transition="width 0.3s ease-in-out"
      h="100vh"
      pos={isDrawer ? 'relative' : 'fixed'}
      top="0"
      left="0"
      zIndex={5}
      boxShadow="lg"
      overflow="visible"
      display="flex"
      flexDirection="column"
    >
      {/* Header Section with Logos and Toggle - Fixed at top */}
      <Box flexShrink={0}>
        {/* Logos Section */}
        <Flex align="center" transition="all 0.3s ease" justify="center" m={3} gap={4} visibility={(isExpanded || isDrawer) ? 'visible' : 'hidden'}>
          <Image
            src={KeralaLogo}
            width="120px"
            height="55px"
            alt="Kerala Logo"
            objectFit="contain"
          />
          <Image
            src={RootsLogo}
            width="150px"
            height="55px"
            alt="Roots Logo"
            objectFit="contain"
          />
        </Flex>
        {/* )} */}

      </Box>

      {/* Scrollable Content Area */}
      <Box
        flex="1"
        overflow="auto"
        css={{
          '&::-webkit-scrollbar': {
            display: 'none'
          },
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none'
        }}
      >
        <VStack spacing={1} mt={4} align="stretch" px={isExpanded || isDrawer ? 4 : 2} pb={4}>
          {NAV_ITEMS.map((item) => {
            const activeMenuItem = getActiveMenuItem();
            const isActive = activeMenuItem === item.label;
            return (
              <Flex
                key={item.label}
                align="center"
                gap={3}
                py={2}
                px={2}
                mx={1}
                // role="group"
                cursor="pointer"
                borderRadius="md"
                bg={isActive ? 'secondary.500' : 'transparent'}
                color={isActive ? 'white' : 'gray.600'}
                _hover={{ bg: isActive ? 'secondary.500' : 'secondary.50' }}
                onClick={() => handleClick(item.path)}
                transition="all 0.2s"
              >
                <Box
                  bg={isActive ? 'white' : 'transparent'}
                  borderRadius="full"
                  p={1.5}
                  // _groupHover={{ bg: isActive ? 'white' : 'primary.50' }}
                  display="flex"
                  alignItems="center"
                  justifyContent="center"
                  minW="32px"
                  minH="32px"
                >
                  <item.icon
                    style={{
                      color: isActive ? '#02579A' : '#4A5568',
                      width: '20px',
                      height: '20px'
                    }}
                  />
                </Box>
                {(isExpanded || isDrawer) && (
                  <Text
                    whiteSpace="nowrap"
                    fontSize="14px"
                    color={isActive ? 'white' : 'primary.A100'}
                    fontWeight={isActive ? '600' : '500'}
                  >
                    {item.label}
                  </Text>
                )}
              </Flex>
            );
          })}
        </VStack>
      </Box>

      {/* Mobile User Actions - Only show in drawer */}
      {isDrawer && (
      <Box
        mt="auto"
        p={4}

      >
        <HeaderActions />
      </Box>
      )}

      {/* Desktop RP Group Card */}
      {(isExpanded || isDrawer) && (
      <Box
        mt="auto"
        mb={isDrawer ? '80px' : '20px'}
        mx="auto"
        w="80%"
        textAlign="center"
      >
        <Image
          src={SidebarCard}
          alt="Sidebar"
          objectFit="contain"
          w="100%"
        />
      </Box>
      )}

      {/* Toggle Button - Positioned on border between sidebar and content */}
      {!isDrawer && (
        <IconButton
          icon={isExpanded ? <ChevronLeftIcon /> : <ChevronRightIcon />}
          onClick={toggleSidebar}
          position="absolute"
          top="70px"
          right="-17px"
          bg="secondary.500"
          color="white"
          borderRadius="full"
          size="sm"
          w="32px"
          h="32px"
          minW="32px"
          zIndex={20}
          boxShadow="lg"
          border="2px solid white"
          aria-label={isExpanded ? 'Collapse Sidebar' : 'Expand Sidebar'}
          _hover={{
            bg: 'secondary.800',
            transform: 'scale(1.1)'
          }}
          _active={{
            bg: 'secondary.900',
            transform: 'scale(0.95)'
          }}
          transition="all 0.2s ease-in-out"
          sx={{
            '& svg': {
              width: '16px',
              height: '16px',
              color: 'white'
            }
          }}
        />
      )}
    </Box>
  );
}

export default SideBar;
