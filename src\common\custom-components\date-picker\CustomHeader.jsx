import { range } from 'lodash-es';
import React, { useState } from 'react';
import { createPortal } from 'react-dom';
import { motion } from 'framer-motion';

const variants = {
  show: {
    opacity: 1,
    transition: {
      ease: 'easeOut',
      duration: 0.5
    }
  },
  hide: {
    opacity: 0,
    transition: {
      ease: 'easeIn',
      duration: 0.2
    }
  }
};

const CustomHeader = (props) => {
  const {
    headerProp, fromYear, toYear, getYear, getMonth
  } = props;

  const [showYear, setShowYear] = useState(false);
  const [showMonth, setShowMonth] = useState(false);

  const datePickerMonthClass = document.getElementsByClassName(
    'react-datepicker__month'
  )[0];

  const years = range(fromYear, toYear + 1);

  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  return (
    <div className="Norka-calendar-headWrapper">
      <div className="Norka-calendar-headSelector">
        <button onClick={() => setShowYear(!showYear)} type="button">
          <span style={{ userSelect: 'none' }}>
            {months[getMonth(headerProp.date)]}
          </span>
          <span style={{ userSelect: 'none' }}>{getYear(headerProp.date)}</span>
          <span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="11"
              height="6"
              viewBox="0 0 11 6"
              fill="none"
            >
              <path
                d="M1.5 1L5.5 5L9.5 1"
                stroke="#09327B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </span>
        </button>
      </div>

      {showYear
        ? createPortal(
          <motion.div
            key={showYear}
            variants={variants}
            animate="show"
            initial="hide"
            className="Norka-calendar-showYear"
          >
            {years.reverse().map((option) => {
              return (
                <button
                  type="button"
                  style={{ userSelect: 'none' }}
                  className={
                      getYear(new Date(headerProp.date)) === option
                        ? 'active'
                        : ''
                    }
                  onClick={(e) => {
                    headerProp.changeYear(e.target.innerText);
                    setShowMonth(true);
                    setShowYear(false);
                  }}
                  key={option}
                >
                  {option}
                </button>
              );
            })}
          </motion.div>,
          datePickerMonthClass
        )
        : null}

      {showMonth
        ? createPortal(
          <motion.div
            key={showYear}
            variants={variants}
            animate="show"
            initial="hide"
            className="Norka-calendar-showMonth"
          >
            {months.map((option) => {
              return (
                <button
                  type="button"
                  className={
                      getMonth(new Date(headerProp.date)) === option
                        ? 'active'
                        : ''
                    }
                  onClick={(e) => {
                    headerProp.changeMonth(
                      months.indexOf(e.target.innerText)
                    );
                    setShowMonth(false);
                  }}
                  key={option}
                >
                  {option}
                </button>
              );
            })}
          </motion.div>,
          datePickerMonthClass
        )
        : null}

      <div className="Norka-calendar-headButtons">
        <button
          aria-label="hidden"
          onClick={headerProp.decreaseMonth}
          disabled={headerProp.CalenderPrevMonthButtonDisabled}
          type="button"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="23"
            height="22"
            viewBox="0 0 23 22"
            fill="none"
          >
            <rect x="0.5" width="22" height="22" rx="11" fill="#F1F6FF" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M14.3582 6.46798L9.60048 10.9046L14.0371 15.6624L12.5891 17.0127L6.80219 10.8069L13.0079 5.01997L14.3582 6.46798Z"
              fill="#09327B"
            />
          </svg>
        </button>

        <button
          aria-label="hidden"
          onClick={headerProp.increaseMonth}
          disabled={headerProp.nextMonthButtonDisabled}
          type="button"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="23"
            height="22"
            viewBox="0 0 23 22"
            fill="none"
            opacity={headerProp.nextMonthButtonDisabled ? 0.3 : 1}
          >
            <rect x="0.5" width="22" height="22" rx="11" fill="#F1F6FF" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M8.64154 6.46798L13.3993 10.9046L8.96262 15.6624L10.4106 17.0127L16.1976 10.8069L9.99183 5.01997L8.64154 6.46798Z"
              fill="#09327B"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};

export default CustomHeader;
