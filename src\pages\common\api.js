import { createApi } from '@reduxjs/toolkit/query/react';
import { API_URL } from 'common';
import { _ } from 'utils/lodash';
import { getBaseQuery, handleAPIRequest } from 'utils/http';
import { AADHAAR_API_RESPONSE_STATUS } from 'common/constants';
import { t } from 'i18next';
import { STATE_REDUCER_KEY } from './constant';
import { actions as commonActions } from './slice';

export const commonApi = createApi({
  reducerPath: STATE_REDUCER_KEY.API,
  baseQuery: getBaseQuery(),
  tagTypes: ['countries', 'gender', 'state', 'district', 'educationQualifications'],
  endpoints: (builder) => ({
    fetchCountry: builder.query({
      query: () => API_URL.SAMPLE.COUNTRY,
      providesTags: ['countries']
    }),
    fetchGender: builder.query({
      query: () => API_URL.COMMON.GENDER,
      providesTags: ['gender']
    }),
    fetchState: builder.query({
      query: (stateId) => API_URL.COMMON.STATE.replace('{stateId}', stateId),
      providesTags: ['state']
    }),
    fetchDistrict: builder.query({
      query: (stateId) => API_URL.COMMON.DISTRICT.replace('{stateId}', stateId),
      providesTags: ['district']
    }),
    fetchEducationQualifications: builder.query({
      query: () => API_URL.COMMON.EDUCATION_QUALIFICATIONS,
      providesTags: ['educationQualifications']
    }),

    // OTP Mutations
    sendMobileOtp: builder.mutation({
      query: (data) => ({
        url: API_URL.OTP.SEND_MOBILE_OTP,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, arg, dispatch) => {
          const { mobile } = arg;
          dispatch(commonActions.setOtpState({
            key: mobile,
            data: {
              otpSuccess: true,
              isVerified: false
            }
          }));
        }
      })
    }),

    sendEmailOtp: builder.mutation({
      query: (data) => ({
        url: API_URL.OTP.SEND_EMAIL_OTP,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        onSuccess: (result, arg, dispatch) => {
          const { email } = arg;
          dispatch(commonActions.setOtpState({
            key: email,
            data: {
              otpSuccess: true,
              isVerified: false
            }
          }));
        }
      })
    }),

    sendAadhaarOtp: builder.mutation({
      query: (data) => ({
        url: API_URL.OTP.SEND_AADHAAR_OTP,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        showSuccessToast: false,
        onSuccess: (result, arg, dispatch) => {
          const { aadhaarNo } = arg;
          const { data: responseData } = result;

          if (responseData?.status === AADHAAR_API_RESPONSE_STATUS.SUCCESS) {
            dispatch(commonActions.setCustomToast({
              open: true,
              variant: 'success',
              message: _.get(responseData, 'message', t('sendOtpSuccess')),
              title: t('sendOtpSuccess')
            }));

            dispatch(commonActions.setOtpState({
              key: aadhaarNo,
              data: {
                otpSuccess: true,
                isVerified: false,
                ...responseData
              }
            }));
          } else {
            dispatch(commonActions.setCustomToast({
              open: true,
              variant: 'error',
              message: responseData?.message || t('failedToSendOtp'),
              title: 'Error'
            }));

            dispatch(commonActions.setOtpState({
              key: aadhaarNo,
              data: {
                otpSuccess: false,
                isVerified: false
              }
            }));
          }
        },
        onError: (_error, arg, dispatch) => {
          const { aadhaarNo } = arg;
          dispatch(commonActions.setOtpState({
            key: aadhaarNo,
            data: {
              otpSuccess: false,
              isVerified: false
            }
          }));
        }
      })
    }),

    verifyOtp: builder.mutation({
      query: (data) => ({
        url: API_URL.OTP.VERIFY_OTP,
        method: 'POST',
        body: data
      }),
      onQueryStarted: handleAPIRequest({
        showSuccessToast: false,
        onSuccess: (result, arg, dispatch) => {
          const { mobile, email, aadhaarNo } = arg;
          const key = mobile || email || aadhaarNo;
          const { data: responseData } = result;

          // Handle Aadhaar verification response
          if (responseData?.status === AADHAAR_API_RESPONSE_STATUS.SUCCESS) {
            dispatch(commonActions.setCustomToast({
              open: true,
              variant: 'success',
              message: _.get(responseData, 'message', t('verifyOtpSuccess')),
              title: t('verifyOtpSuccess')
            }));

            dispatch(commonActions.setOtpState({
              key,
              data: {
                isVerified: true,
                otpSuccess: false,
                verificationData: responseData
              }
            }));
          } else {
            dispatch(commonActions.setCustomToast({
              open: true,
              variant: 'error',
              message: t('invalidType ', { type: t('otp') }),
              title: 'Error'
            }));

            dispatch(commonActions.setOtpState({
              key,
              data: {
                isVerified: false,
                otpSuccess: true
              }
            }));
          }
        }
      })
    })
  })
});

export const {
  useFetchCountryQuery,
  useFetchGenderQuery,
  useFetchStateQuery,
  useFetchDistrictQuery,
  useFetchEducationQualificationsQuery,
  useSendMobileOtpMutation,
  useSendEmailOtpMutation,
  useSendAadhaarOtpMutation,
  useVerifyOtpMutation
} = commonApi;
