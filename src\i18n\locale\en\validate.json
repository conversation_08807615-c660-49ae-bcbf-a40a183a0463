{"noDataFound": "No data found", "documentFetchFailed": "Document Fetch Failed", "noDocumentFound": "No Document Found", "notFound": "Sorry, the page or resource you are looking for could not be found.", "serverError": "Sorry, something went wrong. Please try again later.", "serviceUnavailable": "Sorry, the service is currently unavailable. Please try again later.", "registrationCompletionText": "To complete your registration, please fill in all the fields below", "otpVerificationInstruction": "Please complete the OTP verification by entering the code sent to your registered email ID or phone number", "otpRequired": "OTP is required", "otpLengthError": "OTP must be 6 digits", "enterVerificationCode": "Enter verification code", "alreadyRegisteredText": "Already registered?", "verificationSuccessTitle": "Verification Successful", "verificationSuccessMessage": "Your account has been created successfully!", "institutionNameRequired": "Institution name is required", "max100Characters": "Maximum 100 characters allowed", "institutionTypeRequired": "Institution type is required", "institutionLocationRequired": "Institution location is required", "yearOfCompletionRequired": "Year of completion is required", "stateOfInstitutionRequired": "State of institution is required", "districtOfInstitutionRequired": "District of institution is required", "currentInstitutionTypeRequired": "Current institution type is required", "currentInstitutionNameRequired": "Current institution name is required", "courseModeRequired": "Course mode is required", "academicYearRequired": "Academic year is required", "dateOfAdmissionRequired": "Date of admission is required", "dateCannotBeFuture": "Date cannot be in the future", "boardRequired": "Board is required", "gradeRequired": "Grade/Percentage is required", "accountHolderNameRequired": "Account holder name is required", "onlyLettersAllowed": "Only letters are allowed", "min3Characters": "Minimum 3 characters required", "max50Characters": "Maximum 50 characters allowed", "accountNumberRequired": "Account number is required", "onlyNumbersAllowed": "Only numbers are allowed", "min9Digits": "Minimum 9 digits required", "max18Digits": "Maximum 18 digits allowed", "bankNameRequired": "Bank name is required", "branchRequired": "Branch is required", "ifscRequired": "IFSC code is required", "enterIFSC": "Enter IFSC code", "invalidIFSCFormat": "Invalid IFSC format (e.g., ABCD0123456)", "onlyNumbersAndDecimalAllowed": "Only numbers and decimal point allowed", "validPercentageRequired": "Please enter a valid percentage (0-100)", "enterParentName": "Enter parent/guardian name", "selectRelationship": "Select relationship", "enterContactNumber": "Enter 10-digit contact number", "enterAnnualIncome": "Enter annual income", "selectCertificateIssuer": "Select certificate issuer", "enterCertificateNo": "Enter certificate number", "enterYourEmail": "Enter your email ID", "enterPravasiIdCardNumber": "Enter Pravasi ID Card Number", "enterPercentageOfDisability": "Enter Percentage of Disability", "enterFirstName": "Enter first name", "enterMiddleName": "Enter middle name", "enterLastName": "Enter last name", "selectGender": "Select gender", "enterAadhaarNumber": "Enter 12-digit <PERSON><PERSON><PERSON><PERSON> number", "enterHouseNumber": "Enter house number/address", "enterStreetLocality": "Enter street/locality", "enterCityTown": "Enter city/town", "selectDistrict": "Select district", "selectState": "Select state", "enterPincode": "Enter 6-digit pincode", "enterMobileNumber": "Enter 10-digit mobile number", "completeRegister": "To complete your registration, please fill in all the fields below", "passwordMatch": "Password must be match"}