import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { isTokenValid, logout } from 'utils/auth';

const RouteAuthorizer = ({ element }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  useEffect(() => {
    const isAuthRoute = location.pathname.includes('/ui/auth/');

    const hasValidToken = isTokenValid();

    if (!hasValidToken && !isAuthRoute) {
      const hadToken = localStorage.getItem('token');
      if (hadToken) {
        logout(dispatch, true);
      } else {
        navigate('/ui/auth/login', { replace: true });
      }
      return;
    }

    if (hasValidToken && isAuthRoute) {
      navigate('/ui/applicant/dashboard', { replace: true });
    }
  }, [location.pathname, navigate, dispatch]);

  return element;
};

export default RouteAuthorizer;
