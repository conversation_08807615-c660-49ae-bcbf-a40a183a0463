export const REQUEST_METHOD = {
  GET: 'GET',
  PUT: 'PUT',
  POST: 'POST',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
  MULTIPART: 'MULTIPART'
};

export const CONTENT_TYPE = {
  APPLICATION_JSON: 'application/json',
  MULTIPART_FORM_DATA: 'multipart/form-data'
};

export const HTTP_HEADERS = {
  'Content-Type': CONTENT_TYPE.APPLICATION_JSON,
  Accept: CONTENT_TYPE.APPLICATION_JSON
};

export const MULTIPART_HEADERS = {
  'Content-Type': CONTENT_TYPE.MULTIPART_FORM_DATA
};

export const STATE = {
  id: 502032,
  code: 'KL',
  name: 'Kerala'
};

export const COUNTRY = {
  id: 501077,
  code: 'COUNTRY_INDIA',
  countryCode: 'IND',
  name: 'India'
};

export const REQUEST_STATUS = {
  PROGRESS: 'PROGRESS',
  SUCCESS: 'SUCCESS',
  FAILED: 'FAILED'
};

export const TOST_CONFIG = {
  duration: 4000,
  isClosable: true,
  status: 'success',
  position: 'top-right',
  variant: 'left-accent'
};

export const DATE_FORMAT = {
  DATE_TIME_GMT: 'ddd MMM DD YYYY HH:mm:ss [GMT]ZZ',
  DATE_YYYYMMDD: 'YYYY-MM-DD',
  DATE_LOCAL: 'DD-MM-YYYY',
  TIME_LOCAL: 'HH:mm:ss',
  DATE_TIME: 'DD-MM-YYYY hh:mm:ss'
};

export const DEFAULT_FILE_ID = 'DEFAULT_FILE_ID';

export const OTP_TYPES = {
  MOBILE: 'mobile',
  EMAIL: 'email',
  AADHAAR: 'aadhaar'
};

// Project and Module Constants for Aadhaar Verification
export const AADHAAR_PROJECT = {
  KSMART: 'KSMART'
};

export const AADHAAR_MODULE = {
  PENSION: 'PENSION'
};

export const AADHAAR_API_RESPONSE_STATUS = {
  FAIL: 'fail',
  SUCCESS: 'success'
};
