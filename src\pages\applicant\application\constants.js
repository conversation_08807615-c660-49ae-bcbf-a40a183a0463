// === State Reducer Key ===
export const STATE_REDUCER_KEY = {
  SLICE: 'application',
  API: 'application-api'
};

// === Bank Options ===
export const BANK_OPTIONS = [
  { code: 'sbi', name: 'State Bank of India' },
  { code: 'hdfc', name: 'HDFC Bank' },
  { code: 'icici', name: 'ICICI Bank' },
  { code: 'axis', name: 'Axis Bank' },
  { code: 'pnb', name: 'Punjab National Bank' }
];

// === Branch Options ===
export const BRANCH_OPTIONS = [
  { code: 'branch1', name: 'Main Branch' },
  { code: 'branch2', name: 'City Center Branch' }
];

// === Board Options ===
export const BOARD_OPTIONS = [
  { code: 'cbse', name: 'CBSE' },
  { code: 'state', name: 'State Board' },
  { code: 'icse', name: 'ICSE' },
  { code: 'ib', name: 'IB' }
];

// === Institution Type Options ===
export const INSTITUTION_TYPE_OPTIONS = [
  { code: 'government', name: 'Government' },
  { code: 'private', name: 'Private' },
  { code: 'aided', name: 'Aided' }
];

// === Year Options ===
export const YEAR_OPTIONS = [
  { code: '2024', name: '2024' },
  { code: '2023', name: '2023' },
  { code: '2022', name: '2022' },
  { code: '2021', name: '2021' },
  { code: '2020', name: '2020' }
];

// === Academic Year Options ===
export const ACADEMIC_YEAR_OPTIONS = [
  { code: '2024-25', name: '2024-25' },
  { code: '2023-24', name: '2023-24' },
  { code: '2022-23', name: '2022-23' }
];

// === Stepper Steps ===
export const STEPPER_STEPS = {
  APPLICANT_DETAILS: 'APPLICANT_DETAILS',
  PARENT_DETAILS: 'PARENT_DETAILS',
  BANK_DETAILS: 'BANK_DETAILS',
  ACADEMIC_DETAILS: 'ACADEMIC_DETAILS',
  DOCUMENTS_UPLOAD: 'DOCUMENTS_UPLOAD',
  REVIEW_SUBMIT: 'REVIEW_SUBMIT'
};

// Care status enum
export const CARE_STATUS = {
  PARENTS: 'PARENTS',
  SINGLE_PARENT: 'SINGLE_PARENT',
  ORPHAN: 'ORPHAN'
};

export const CARE_STATUS_OPTIONS = [
  { code: CARE_STATUS.PARENTS, name: 'Under The Care Of Parents' },
  { code: CARE_STATUS.SINGLE_PARENT, name: 'Under The Care Of A Single Parent' },
  { code: CARE_STATUS.ORPHAN, name: 'Orphan (No Living Parents)' }
];

// === Current Care Provider Enum & Options ===
export const CURRENT_CARE_PROVIDER = {
  GUARDIAN: 'GUARDIAN',
  INSTITUTION: 'INSTITUTION'
};

export const CURRENT_CARE_PROVIDER_OPTIONS = [
  { code: CURRENT_CARE_PROVIDER.GUARDIAN, name: 'Guardian' },
  { code: CURRENT_CARE_PROVIDER.INSTITUTION, name: 'Institution' }
];

// === Income Certificate Issuer Enum & Options ===
export const INCOME_CERTIFICATE_ISSUER = {
  TAHSILDAR: 'TAHSILDAR',
  VILLAGE_OFFICER: 'VILLAGE_OFFICER'
};

export const INCOME_CERTIFICATE_ISSUER_OPTIONS = [
  { code: INCOME_CERTIFICATE_ISSUER.TAHSILDAR, name: 'Tahsildar' },
  { code: INCOME_CERTIFICATE_ISSUER.VILLAGE_OFFICER, name: 'Village Officer' }
];

// === Relationship Enum & Options ===
export const RELATIONSHIP = {
  FATHER: 'FATHER',
  MOTHER: 'MOTHER'
};

export const RELATIONSHIP_OPTIONS = [
  { code: RELATIONSHIP.FATHER, name: 'Father' },
  { code: RELATIONSHIP.MOTHER, name: 'Mother' }
];

// Family circumstances enum
export const FAMILY_CIRCUMSTANCES = {
  ORPHAN: 'ORPHAN',
  SINGLE_PARENT_BEDRIDDEN: 'SINGLE_PARENT_BEDRIDDEN',
  SINGLE_PARENT_HOUSEHOLD: 'SINGLE_PARENT_HOUSEHOLD',
  BOTH_PARENTS_BEDRIDDEN: 'BOTH_PARENTS_BEDRIDDEN'
};

export const FAMILY_CIRCUMSTANCES_OPTIONS = [
  { code: FAMILY_CIRCUMSTANCES.ORPHAN, name: 'Orphan' },
  { code: FAMILY_CIRCUMSTANCES.SINGLE_PARENT_BEDRIDDEN, name: 'Single Parent Bedridden/Terminally Ill' },
  { code: FAMILY_CIRCUMSTANCES.SINGLE_PARENT_HOUSEHOLD, name: 'Single Parent Household' },
  { code: FAMILY_CIRCUMSTANCES.BOTH_PARENTS_BEDRIDDEN, name: 'Both Parents Bedridden/Terminally Ill' }
];

export const COURSE_NAME_OPTIONS = [
  { code: 'ba', name: 'Bachelor of Arts (BA)' },
  { code: 'bsc', name: 'Bachelor of Science (BSc)' },
  { code: 'bcom', name: 'Bachelor of Commerce (BCom)' },
  { code: 'bba', name: 'Bachelor of Business Administration (BBA)' },
  { code: 'bpharm', name: 'Bachelor of Pharmacy (BPharm)' }
];

export const INSTRUCTION_TYPE_OPTIONS = [
  { code: 'regular', name: 'Regular' },
  { code: 'distance', name: 'Distance Education' },
  { code: 'online', name: 'Online' },
  { code: 'part-time', name: 'Part-time' },
  { code: 'correspondence', name: 'Correspondence' }
];
