import {
  <PERSON>, <PERSON><PERSON>, Card, Text, VStack, Heading, useToast
} from 'common/components';
import FormController from 'common/components/FormController';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { useResetPasswordMutation } from '../api';

const ChangePassword = () => {
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setError
  } = useForm({
    defaultValues: {
      newPassword: '',
      confirmPassword: '',
      otp: ''
    }
  });

  const toast = useToast();
  const navigate = useNavigate();
  const [resetPassword] = useResetPasswordMutation();

  const onSubmit = async (data) => {
    try {
      const response = await resetPassword({
        newPassword: data.newPassword,
        otp: data.otp
      }).unwrap();

      toast({
        title: 'Password Changed',
        description: response.message || 'Your password has been updated successfully',
        status: 'success',
        duration: 5000,
        isClosable: true,
        position: 'top-right'
      });

      navigate('/ui/auth/login');
    } catch (error) {
      if (error.data?.field) {
        setError(error.data.field, {
          type: 'manual',
          message: error.data.message
        });
      } else {
        toast({
          title: 'Error',
          description: error.data?.message || 'Failed to update password',
          status: 'error',
          duration: 5000,
          isClosable: true,
          position: 'top-right'
        });
      }
    }
  };

  const handleResendOTP = () => {
    // Implement OTP resend logic here
    toast({
      title: 'OTP Resent',
      description: 'A new verification code has been sent to your email',
      status: 'info',
      duration: 3000,
      isClosable: true,
      position: 'top-right'
    });
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
      maxW={{ md: '600px' }}
      mx="auto"
    >
      <VStack spacing={8} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={4} textAlign="center">
          <Heading fontSize="3xl" fontWeight="bold" color="gray.700">
            Change Password
          </Heading>
          <Text fontSize="md" color="gray.500">
            Create a new password for your account
          </Text>
        </VStack>

        <VStack spacing={6} w="full" mt={12}>
          <FormController
            type="password"
            name="newPassword"
            control={control}
            label="New Password"
            placeholder="Enter your new password"
            rules={{
              required: 'New password is required',
              minLength: {
                value: 8,
                message: 'Password must be at least 8 characters'
              },
              pattern: {
                value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                message: 'Must contain uppercase, lowercase, number and special character'
              }
            }}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="password"
            name="confirmPassword"
            control={control}
            label="Confirm Password"
            placeholder="Re-enter your new password"
            rules={{
              required: 'Please confirm your password',
              validate: (value) => value === watch('newPassword') || 'Passwords do not match'
            }}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="otp"
            name="otp"
            control={control}
            label="Verification Code"
            rules={{
              required: 'OTP is required',
              minLength: {
                value: 6,
                message: 'Code must be 6 digits'
              }
            }}
            errors={errors}
            otpProps={{
              placeholder: 'XXXXXX',
              maxLength: 6,
              isNumeric: true
            }}
          />

          <Text fontSize="sm" color="gray.500" textAlign="center">
            We&apos;ve sent a 6-digit code to your registered email
          </Text>

          <Button
            variant="link"
            color="primary.A100"
            fontSize="sm"
            onClick={handleResendOTP}
            _hover={{ textDecoration: 'underline' }}
          >
            Resend Code
          </Button>

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 6 }}
            isLoading={isSubmitting}
            loadingText="Updating..."
            height="50px"
            fontSize="md"
          >
            Update Password
          </Button>

          <VStack spacing={1} fontSize="md" mt={6}>
            <Link
              to="/ui/auth/login"
              style={{
                color: 'primary.A100',
                textDecoration: 'underline',
                fontWeight: '500'
              }}
            >
              Back to Login
            </Link>
          </VStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default ChangePassword;
