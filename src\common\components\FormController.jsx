import { Checkbox, Input } from '@chakra-ui/react';
import { OTP_TYPES } from 'common/constants';
import {
  ErrorText, Select, TextInput, DatePicker, RadioButton, TextArea
} from 'common/custom-components';
import DocumentUploadCard from 'common/custom-components/document-upload/DocumentUpload';
import OtpInput from 'common/custom-components/otp-input';
import { Controller } from 'react-hook-form';
import { _ } from 'utils/lodash';

const FormController = (props) => {
  const {
    type, name, control, label, errors, optionKey = 'code', handleChange,
    otpProps = {}
  } = props;

  const error = _.get(errors, `${name}.message`, null);

  switch (type) {
    case 'time':
    case 'date':
    case 'datetime':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <DatePicker
              {...field}
              {...props}
              {...{ error }}
              onChange={(e) => {
                field.onChange(e);
                props?.handleChange?.(e);
              }}
            />
          )}
        />
      );
    case 'check':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <>
              <Checkbox
                {...field}
                {...props}
                onChange={(e) => {
                  field.onChange(e);
                  props?.handleChange?.(e);
                }}
                isChecked={field?.value || false}
              >{label}
              </Checkbox>
              {error && <ErrorText error={error} />}
            </>
          )}
        />
      );
    case 'select':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <div className="flex-1">
              <Select
                {...field}
                {...props}
                {...{ error }}
                onChange={(e) => {
                  field.onChange(e ? e[optionKey] : e);
                  props?.handleChange?.(e);
                }}
              />
            </div>
          )}
        />
      );
    case 'textarea':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <TextArea
              {...field}
              {...props}
              {...{ error }}
              onChange={(e) => {
                field.onChange(e);
                props?.handleChange?.(e);
              }}
            />
          )}
        />
      );
    case 'radio':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <RadioButton
              {...field}
              {...props}
              {...{ error }}
              onChange={(e) => {
                field.onChange(e);
                props?.handleChange?.(e);
              }}
            />
          )}
        />
      );
    case 'file':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <DocumentUploadCard
              {...field}
              {...props}
              {...{ error }}
              onFileUpload={(e) => {
                field.onChange(e);
                props?.handleChange?.(e);
              }}
            />
          )}
        />
      );
    case 'otp':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <Input
              {...field}
              {...props}
              {...{ error }}
              placeholder={otpProps.placeholder || 'Enter OTP'}
              type="text"
              maxLength={otpProps.maxLength || 6}
              textAlign="center"
              fontSize="xl"
              letterSpacing="0.5em"
              onChange={(e) => {
                const value = e.target.value.replace(/\D/g, '');
                field.onChange(value);
                props?.handleChange?.(value);
              }}
            />
          )}
        />
      );
    case 'otpInput':
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => {
            const { value } = field;
            const {
              type: _unwantedType, otpType = OTP_TYPES.AADHAAR, verified, ...otherProps
            } = props;
            return <OtpInput otpType={otpType} value={value || ''} verified={verified || false} {...{ isDisabled: !!value }} {...otherProps} />;
          }}
        />
      );
    default:
      return (
        <Controller
          name={name}
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              {...props}
              {...{ error }}
              onChange={(e) => {
                field.onChange(e);
                handleChange?.(e.target.value);
              }}
            />
          )}
        />
      );
  }
};

export default FormController;
