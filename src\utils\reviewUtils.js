import { convertToDDMMYYYY } from 'utils/date';
import { _ } from 'utils/lodash';

// === Review Display Helpers ===

/**
 * Format date for display in DD-MM-YYYY format
 * @param {string} dateString - Date string from API
 * @returns {string} - Formatted date or empty string
 */
const formatDisplayDate = (dateString) => {
  if (!dateString) return '';
  try {
    return convertToDDMMYYYY(dateString);
  } catch (error) {
    return '';
  }
};

/**
 * Convert boolean to Yes/No text
 * @param {boolean} value - Boolean value
 * @returns {string} - 'Yes' or 'No'
 */
const formatBooleanToText = (value) => {
  if (_.isNil(value)) return '';
  return value ? 'Yes' : 'No';
};

/**
 * Get display value with fallback
 * @param {any} value - Value to display
 * @param {string} fallback - Fallback text
 * @returns {string} - Display value or fallback
 */
const getDisplayValue = (value, fallback = '') => {
  if (_.isNil(value) || value === '') return fallback;
  return String(value);
};

/**
 * Get nested object property safely
 * @param {object} obj - Object to get property from
 * @param {string} path - Property path
 * @param {string} fallback - Fallback value
 * @returns {string} - Property value or fallback
 */
const getNestedValue = (obj, path, fallback = '') => {
  const value = _.get(obj, path);
  return getDisplayValue(value, fallback);
};

/**
 * Format family circumstances for display
 * @param {object} applicationDetails - Application details object
 * @returns {string} - Family circumstances text
 */
const formatFamilyCircumstances = (applicationDetails) => {
  const {
    orphan,
    singleParentAndBedridden,
    singleParent,
    bothParentsAndBedridden
  } = applicationDetails;

  if (orphan) return 'Orphan';
  if (singleParentAndBedridden) return 'Single Parent Bedridden/Terminally Ill';
  if (singleParent) return 'Single Parent Household';
  if (bothParentsAndBedridden) return 'Both Parents Bedridden/Terminally Ill';
  return 'Normal Family';
};

export {
  formatDisplayDate,
  formatBooleanToText,
  getDisplayValue,
  getNestedValue,
  formatFamilyCircumstances
};
