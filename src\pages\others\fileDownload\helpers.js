import { FILE_HEX_SIGNATURE } from './constant';

export function b64toBlob(b64Data, contentType = '', sliceSize = 512) {
  const byteCharacters = atob(b64Data);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);
    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i += 1) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }
  return new File(byteArrays, 'download', { type: contentType });
}

export const getFileDetailsFromStream = (stream, name) => {
  const uintArray = new Uint8Array(stream);
  let header = '';
  for (let i = 0; i < uintArray.length && i < 4; i += 1) {
    header += uintArray[i].toString(16);
  }
  const { ext = 'pdf', type = 'application/pdf' } = FILE_HEX_SIGNATURE[header];
  const blob = new Blob([stream], { type });
  const localURL = URL.createObjectURL(blob);
  return {
    url: localURL, type, size: blob.size, ext, name: `${name}.${ext}`
  };
};

export const generateLocalURLFromBlob = (response, type = 'image/jpeg') => {
  try {
    const blob = b64toBlob(response, type);
    const url1 = window.URL.createObjectURL(blob);
    return {
      url: url1,
      size: blob.size,
      type,
      ext: type.split('/').length === 2 ? type.split('/')[1] : 'jpeg'
    };
  } catch (fileResponseError) {
    return {
      url: '', type: 'invalid/invalid', size: 0, ext: 'invalid'
    };
  }
};

export const getFileContent = (file) => {
  const { type, size, name } = file || {};
  const url = URL.createObjectURL(file);
  return {
    id: name, url, type, size, ext: name?.split('.').pop() || 'unknown', name
  };
};
