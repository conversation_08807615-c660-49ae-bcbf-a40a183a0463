import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.input__container': {
    width: '100%',
    position: 'relative',
    height: '50px',
    'p.form-label': {
      position: 'absolute',
      top: '-8px',
      left: '16px',
      padding: '0 6px',
      fontSize: '13px',
      lineHeight: '16px',
      fontWeight: '400',
      background: `${colors.white}`,
      color: `${colors.primary[900]}`,
      zIndex: 2
    },
    '.chakra-input__group': {
      height: '100%',
      position: 'relative',
      '&.has-left-icon input': {
        paddingLeft: '50px'
      },
      input: {
        padding: '0 16px',
        height: '100%',
        width: '100%',
        fontSize: '15px',
        lineHeight: '20px',
        background: `${colors.white}`,
        border: `1px solid ${colors.gray[300]}`,
        borderRadius: '8px',
        color: `${colors.gray[700]}`,
        fontWeight: '400',
        transition: 'all 0.2s ease-in-out',
        '::placeholder': {
          color: `${colors.gray[400]}`,
          fontSize: '15px',
          opacity: '0.6'
        },
        ':is(:hover, :focus)': {
          border: `1px solid ${colors.tertiary[500]}`
        },
        ':is(:disabled, :read-only)': {
          background: `${colors.gray[50]}`,
          borderColor: `${colors.gray[200]}`,
          cursor: 'not-allowed',
          opacity: 0.9
        },
        '&.error': {
          border: `1px solid ${colors.red[500]}`
        }
      },
      '.custom-right-input-content': {
        position: 'absolute',
        top: 1,
        bottom: 0,
        right: '45px',
        display: 'flex',
        alignItems: 'center'
      },
      '.custom-left-input-content': {
        position: 'absolute',
        top: 1,
        bottom: 0,
        left: '23px',
        display: 'flex',
        alignItems: 'center',
        zIndex: 1
      }
    }
  }
};

export default { component, style };
