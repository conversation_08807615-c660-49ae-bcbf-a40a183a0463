import { Box, Image, PDFViewer } from 'common/components';
import { VALID_FILE_TYPE } from 'pages/common/constant';
import React, { useRef } from 'react';
import { _ } from 'utils/lodash';

const ImagePreview = React.forwardRef(({ src, zoom, rotate }, ref) => (
  <Box
    transform={`scale(${zoom})`}
    transformOrigin="top left"
    transition="transform 0.2s"
    display="inline-block"
  >
    <Box
      transform={`rotate(${rotate}deg)`}
      transition="transform 0.2s"
      transformOrigin="center"
      display="inline-block"
    >
      <Image
        ref={ref}
        src={src}
        alt={`${src}-preview`}
        display="block"
        h="auto"
      />
    </Box>
  </Box>
));

const PdfPreview = React.forwardRef(({ src, zoom, rotate }, ref) => {
  return (
    <Box
      ref={ref}
      transform={`rotate(${rotate}deg)`}
      transformOrigin="center"
      transition="transform 0.2s"
    >
      <PDFViewer fileUrl={src} zoom={zoom} />
    </Box>
  );
});

function DocumentView(props) {
  const { fileData: { url: src, type = '' } = {}, zoom = 1, rotate = 0 } = props;
  const previewRef = useRef();

  if (_.startsWith(type, 'image') && src) {
    return <ImagePreview ref={previewRef} src={src} zoom={zoom} rotate={rotate} />;
  }
  if (type === _.head(VALID_FILE_TYPE) && src) {
    return <PdfPreview ref={previewRef} src={src} zoom={zoom} rotate={rotate} />;
  }
  return <div>Unsupported file type</div>;
}

export default DocumentView;
