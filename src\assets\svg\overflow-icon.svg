<svg width="44" height="25" viewBox="0 0 44 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1_4)">
<rect x="7" y="3.5" width="30" height="10" rx="5" fill="#E8ECEE" shape-rendering="crispEdges"/>
<circle cx="13" cy="8.5" r="2" fill="#00B2EC"/>
<circle cx="22" cy="8.5" r="2" fill="#00B2EC"/>
<circle cx="31" cy="8.5" r="2" fill="#00B2EC"/>
</g>
<defs>
<filter id="filter0_d_1_4" x="0" y="0.5" width="44" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="3.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.13 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_4"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_4" result="shape"/>
</filter>
</defs>
</svg>
