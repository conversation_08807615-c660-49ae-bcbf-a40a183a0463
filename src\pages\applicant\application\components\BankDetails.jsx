import {
  Box, CustomAlert, FormController, Grid, GridItem, t, TitledCard
} from 'common/components';
import React from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { WarningIcon } from 'assets/svg';
import { StepperButtons } from 'common/custom-components';
import { bankDetailsSchema } from '../validation/bankDetails';
import { BANK_OPTIONS, BRANCH_OPTIONS } from '../constants';
import { useSaveBankDetailsMutation } from '../api';

const BankDetails = ({ onNext, onPrevious }) => {
  const {
    control, handleSubmit, formState: { errors }
  } = useForm({
    mode: 'onChange',
    resolver: yupResolver(bankDetailsSchema),
    defaultValues: {
      accountHolderName: '',
      accountNumber: '',
      bankName: '',
      branch: '',
      ifsc: ''
    }
  });
  const [saveBankDetails] = useSaveBankDetailsMutation();

  const onSubmit = async (data) => {
    await saveBankDetails(data).unwrap();
    if (onNext) {
      onNext(data);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <TitledCard title={t('bankDetails')}>
        <Box p={6}>
          <Box mb={10}>
            <CustomAlert
              title={t('importantNote')}
              message={t('bankAccountRequirement')}
              icon={WarningIcon}
              bg="orange.50"
              iconColor="orange.500"
              textColor="orange.700"
            />
          </Box>

          <Grid templateColumns="repeat(12, 1fr)" gap={6}>
            {/* Name of the Account Holder */}
            <GridItem colSpan={[12, 6]}>
              <FormController
                type="text"
                label={t('accountHolderName')}
                name="accountHolderName"
                control={control}
                errors={errors}
                placeholder={t('enterName')}
                required
              />
            </GridItem>

            {/* Account Number */}
            <GridItem colSpan={[12, 6]}>
              <FormController
                type="text"
                label={t('accountNumber')}
                name="accountNumber"
                control={control}
                errors={errors}
                placeholder={t('enterAccountNumber')}
                required
              />
            </GridItem>

            {/* Bank Name */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('bankName')}
                name="bankName"
                control={control}
                errors={errors}
                options={BANK_OPTIONS}
                optionKey="code"
                required
                placeholder={t('select')}
              />
            </GridItem>

            {/* Branch */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="select"
                label={t('branch')}
                name="branch"
                control={control}
                errors={errors}
                options={BRANCH_OPTIONS}
                optionKey="code"
                required
                placeholder={t('select')}
              />
            </GridItem>

            {/* IFSC */}
            <GridItem colSpan={[12, 6, 4]}>
              <FormController
                type="text"
                label={t('ifsc')}
                name="ifsc"
                control={control}
                errors={errors}
                placeholder={t('enterIFSC')}
                required
                transformInput={(value) => value.toUpperCase()}
              />
            </GridItem>
          </Grid>
        </Box>
      </TitledCard>

      {/* Action Buttons */}
      <Box mt={6}>
        <StepperButtons
          currentStep={2}
          totalSteps={6}
          onNext={onNext}
          onPrevious={onPrevious}
          layout="space-between"
        />
      </Box>
    </form>
  );
};

export default BankDetails;
