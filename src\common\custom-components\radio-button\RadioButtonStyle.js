import colors from 'theme/foundations/colors';

const style = {
  // Global radio button overrides for modern design
  '.chakra-radio': {
    alignItems: 'center !important',
    marginRight: '32px !important',

    '.chakra-radio__control': {
      width: '18px !important',
      height: '18px !important',
      borderRadius: '50% !important',
      borderWidth: '2px !important',
      borderColor: `${colors.gray[400]} !important`,
      backgroundColor: 'transparent !important',
      transition: 'all 0.15s ease-in-out !important',

      // Remove default Chakra inner circle
      '&::before': {
        display: 'none !important'
      },

      // Checked state - green circle with white dot
      '&[data-checked]': {
        backgroundColor: 'secondary.500 !important',
        borderColor: 'secondary.500 !important',
        border: 'none !important',
        position: 'relative !important',

        '&::after': {
          content: '""',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '6px',
          height: '6px',
          borderRadius: '50%',
          backgroundColor: 'white !important',
          display: 'block !important',
          zIndex: 1
        }
      },

      // Focus state
      '&:focus, &[data-focus]': {
        boxShadow: 'none !important',
        outline: 'none !important'
      },

      // Hover state for unchecked
      '&:not([data-checked]):hover': {
        borderColor: `${colors.gray[500]} !important`
      }
    }

  },
  '.radio-error': {
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: '0.75rem',
    lineHeight: '14px',
    color: 'red.500',
    marginTop: '4px'
  },
  '.input__container.tabed-radio': {
    margin: '7px 0',
    fieldset: {
      height: 'auto',
      padding: '0',
      borderRadius: '30px'
    }
  },
  '.input__container.normal-radio': {
    height: 'auto',
    margin: '7px 0',
    fieldset: {
      border: 'none',
      height: 'auto',
      paddingTop: '5px'
    },
    // Custom radio button styling with responsive spacing
    '.chakra-radio': {
      alignItems: 'center !important',
      marginRight: '32px !important', // Increased gap between radio options
      marginBottom: '8px !important', // Add bottom margin for mobile stacking

      '@media (max-width: 480px)': {
        marginRight: '16px !important', // Reduced spacing on mobile
        marginBottom: '12px !important', // More bottom margin on mobile
        width: '100% !important' // Full width on mobile for better touch targets
      },

      '.chakra-radio__control': {
        width: '18px !important',
        height: '18px !important',
        borderRadius: '50% !important',
        borderWidth: '2px !important',
        borderColor: `${colors.gray[400]} !important`,
        backgroundColor: 'transparent !important',
        transition: 'all 0.15s ease-in-out !important',
        marginRight: '12px !important', // Gap between circle and label

        '@media (max-width: 480px)': {
          width: '20px !important', // Slightly larger on mobile for better touch
          height: '20px !important',
          marginRight: '10px !important'
        },

        // Remove default Chakra inner circle
        '&::before': {
          display: 'none !important'
        },

        // Checked state - green circle with white dot
        '&[data-checked]': {
          backgroundColor: 'secondary.500 !important',
          borderColor: 'secondary.500 !important',
          border: 'none !important',
          position: 'relative !important',

          '&::after': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '7px',
            height: '7px',
            borderRadius: '50%',
            backgroundColor: 'white !important',
            display: 'block !important',
            zIndex: 1
          }
        },

        // Focus state
        '&:focus': {
          boxShadow: 'none !important',
          outline: 'none !important'
        },

        // Hover state for unchecked
        '&:not([data-checked]):hover': {
          borderColor: `${colors.gray[500]} !important`
        },

        // Disabled state
        '&[data-disabled]': {
          opacity: '0.5 !important',

          '&:hover': {
            borderColor: `${colors.gray[300]} !important`,
            transform: 'none !important'
          },

          // Selected disabled radio button - more highlighted
          '&[data-checked]': {
            opacity: '0.8 !important'
          }
        }
      },

      '.chakra-radio__label': {
        fontSize: '14px !important',
        fontWeight: '400 !important',
        color: `${colors.gray[700]} !important`,
        marginLeft: '0px !important', // Reset since we use control margin
        cursor: 'pointer !important',
        userSelect: 'none !important',
        lineHeight: '1.4 !important',

        '@media (max-width: 480px)': {
          fontSize: '15px !important', // Slightly larger on mobile for better readability
          lineHeight: '1.5 !important',
          paddingTop: '2px !important' // Better alignment with larger radio button
        }
      },

      // Disabled state for entire radio button
      '&[data-disabled]': {
        cursor: 'not-allowed !important',
        pointerEvents: 'none !important',

        '.chakra-radio__label': {
          cursor: 'not-allowed !important'
        },

        // Selected disabled radio button label - slightly darker and bolder
        '&[data-checked] .chakra-radio__label': {
          fontWeight: '500 !important'
        }
      }
    }
  },
  '.input__container.outlined-radio': {
    margin: '7px 0',
    fieldset: {
      height: 'auto',
      paddingTop: '13px',
      border: 'none'
    },

    // Apply radio button styling to outlined variant as well
    '.chakra-radio': {
      alignItems: 'center !important',
      marginRight: '32px !important',

      '.chakra-radio__control': {
        width: '18px !important',
        height: '18px !important',
        borderRadius: '50% !important',
        borderWidth: '2px !important',
        borderColor: `${colors.gray[400]} !important`,
        backgroundColor: 'transparent !important',
        transition: 'all 0.15s ease-in-out !important',
        marginRight: '12px !important',

        '&::before': {
          display: 'none !important'
        },

        '&[data-checked]': {
          backgroundColor: 'secondary.500 !important',
          borderColor: 'secondary.500 !important',
          border: 'none !important',
          position: 'relative !important',

          '&::after': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: 'white !important',
            display: 'block !important',
            zIndex: 1
          }
        },

        '&:focus': {
          boxShadow: 'none !important',
          outline: 'none !important'
        },

        '&:not([data-checked]):hover': {
          borderColor: `${colors.gray[500]} !important`
        },

        // Disabled state
        '&[data-disabled]': {
          cursor: 'not-allowed !important',
          pointerEvents: 'none !important',
          opacity: '0.5 !important',

          '&:hover': {
            transform: 'none !important'
          },

          // Selected disabled radio button - more highlighted
          '&[data-checked]': {
            opacity: '0.8 !important'
          }
        }
      },

      '.chakra-radio__label': {
        fontSize: '14px !important',
        fontWeight: '400 !important',
        color: `${colors.gray[700]} !important`,
        marginLeft: '0px !important',
        cursor: 'pointer !important',
        userSelect: 'none !important'
      },

      // Disabled state for entire radio button
      '&[data-disabled]': {
        cursor: 'not-allowed !important',
        pointerEvents: 'none !important',

        '.chakra-radio__label': {
          cursor: 'not-allowed !important'
        }
      }
    }
  },
  '.tabed': {
    '.chakra-radio[data-checked]': {
      background: `${colors.white}`,
      borderRadius: '30px'
    },
    '.chakra-stack': {
      display: 'flex',
      justifyContent: 'center',
      background: `${colors.primary[500]}`,
      width: '100%',
      borderRadius: '30px',
      padding: '15px 10px',
      '.chakra-radio': {
        display: 'flex',
        flexGrow: 1,
        padding: '0px 30px'
      }
    }
  },
  '.chakra-radio-group.outlined': {
    paddingTop: '5px'
  },
  '.css-11dk4t8[data-checked]': {
    background: 'white !important',
    borderColor: 'secondary.500 !important',
    color: 'white!important'
  }
};

const component = {
  baseStyle: {
    control: {
      width: '18px',
      height: '18px',
      borderRadius: '50%',
      borderColor: `${colors.gray[400]}`,
      borderWidth: '2px',
      backgroundColor: 'transparent',
      transition: 'all 0.15s ease-in-out',

      // Remove default Chakra inner circle
      _before: {
        display: 'none'
      },

      // Checked state - green circle with white dot
      _checked: {
        backgroundColor: 'secondary.500',
        borderColor: 'secondary.500',
        border: 'none',
        position: 'relative',

        _after: {
          content: '""',
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: '6px',
          height: '6px',
          borderRadius: '50%',
          backgroundColor: 'white',
          display: 'block',
          zIndex: 1
        }
      },

      // Focus state
      _focus: {
        boxShadow: 'none',
        outline: 'none'
      },

      // Hover state for unchecked
      _hover: {
        borderColor: `${colors.gray[500]}`,
        transform: 'scale(1.05)',

        _checked: {
          transform: 'none'
        }
      },

      // Disabled state
      _disabled: {
        cursor: 'not-allowed',
        pointerEvents: 'none',
        opacity: 0.6,

        _hover: {
          transform: 'none'
        }
      }
    }
  }
};

export default { component, style };
