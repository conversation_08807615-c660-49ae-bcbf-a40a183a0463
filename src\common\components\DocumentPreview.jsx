import { DocumentModal, DocumentView } from 'common/custom-components';
import {
  Box, CircularProgress, Flex, Grid, Heading, IconButton, t,
  useDisclosure
} from 'common/components';
import { useDownloadFileMutation, useDownloadMultiFileMutation } from 'pages/others/fileDownload/api';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { _ } from 'utils/lodash';
import {
  ChevronLeftIcon, ChevronRightIcon, CloudFailed, ExpandIcon
} from 'assets/svg';
import { DEFAULT_FILE_ID } from 'common/constants';
import { getDocuments } from 'pages/others/fileDownload/selectors';
import { selectorWithKey } from 'utils/common';

const NextPrevious = ({ currentIdx = 0, length = 0, handleClick = () => {} }) => {
  return (
    <Flex maxW="80%">
      <IconButton
        position="absolute"
        borderRadius="2px"
        top="45%"
        left="0"
        isDisabled={currentIdx === 0}
        variant="primary_outline"
        onClick={() => handleClick(currentIdx - 1)}
        icon={<ChevronLeftIcon />}
        py={5}
      />
      <IconButton
        position="absolute"
        borderRadius="2px"
        top="45%"
        right="0"
        isDisabled={length === (currentIdx + 1)}
        variant="primary_outline"
        onClick={() => handleClick(currentIdx + 1)}
        icon={<ChevronRightIcon />}
        py={5}
      />
    </Flex>
  );
};

const DocumentPreview = ({
  payload = {}, isMulti = false, action, docList = [],
  zoom = 0.7, expandView = true, id = DEFAULT_FILE_ID
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedIdx, setSelectedIdx] = useState(0);

  const [downloadMultiFile, {
    error: docError, isLoading: multiLoad
  }] = useDownloadMultiFileMutation();
  const [downloadFile, { error, isLoading: load }] = useDownloadFileMutation();

  const docInStore = selectorWithKey(useSelector(getDocuments), id) || '';

  const documents = docInStore || docList || [];
  const documentError = error || docError;
  const documentLoading = multiLoad || load;

  useEffect(() => {
    if (action) {
      action();
    } else if (!_.isEmpty(payload)) {
      if (isMulti) downloadMultiFile({ id, ...payload });
      else downloadFile({ id, ...payload });
    }
  }, [isMulti, JSON.stringify(payload)]);

  const selectedDocument = documents[selectedIdx] || {};

  const renderComponents = () => {
    if (documentError) {
      return (
        <>
          <CloudFailed width="150" height="150" />
          <p className="font-bold text-[20px] text-[#09327B]">{t('documentFetchFailed')}</p>
        </>
      );
    }
    if (documentLoading) {
      return (
        <CircularProgress
          isIndeterminate
          size="40px"
          thickness="8px"
          trackColor="secondary.500"
          color="primary.500"
        />
      );
    }
    if (!_.isEmpty(selectedDocument)) {
      return (
        <DocumentView fileData={selectedDocument} key={selectedDocument?.name} zoom={zoom} />
      );
    }
    return (
      <Heading as="h2" fontSize="lg" mt={2} color="primary.500">
        {t('noDocumentFound')}
      </Heading>
    );
  };

  return (
    <Box width="max-content">
      <Grid direction="column" justifyItems="center" textAlign="center" w="max-content" p={5} pos="relative">
        {!_.isEmpty(selectedDocument) && expandView && (
        <IconButton
          icon={<ExpandIcon />}
          size="sm"
          transform="scale(0.8)"
          variant="secondary_outline"
          aria-label="Preview"
          position="absolute"
          borderRadius="50%"
          padding={2}
          top="1.4rem"
          right="1.4rem"
          zIndex={9}
          onClick={() => onOpen(true)}
        />
        )}
        {renderComponents()}
        {documents?.length > 1
          && (
          <NextPrevious
            currentIdx={selectedIdx}
            length={documents?.length}
            handleClick={setSelectedIdx}
          />
          )}
      </Grid>
      <DocumentModal {...{
        onOpen, onClose, isOpen, fileData: selectedDocument
      }}
      />
    </Box>
  );
};

export default DocumentPreview;
