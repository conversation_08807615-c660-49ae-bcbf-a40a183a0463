{"aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarId": "Aadhaar <PERSON>", "aadhaarLinkedMobileNo": "<PERSON><PERSON><PERSON><PERSON> Linked Mobile Number", "aadhaarNo": "<PERSON><PERSON><PERSON><PERSON>", "aadharUidNotAvailable": "Aadhar/ UID Not Available", "acknowledgment": "Acknowledgement", "actions": "Actions", "addMore": "Add More", "address": "Address", "advancedSearch": "Advanced Search", "age": "Age", "ageUnit": "Age Unit", "alert": "<PERSON><PERSON>", "applicant": "Applicant", "applicantDetails": "Applicant Details", "applicantType": "Applicant Type", "application": "Application", "applicationNo": "Application No", "applicationNumber": "Application Number", "assignee": "Assignee", "attachDocument": "Attach Document", "attachDocuments": "Attach Documents", "attachmentsOf": "Attachments of", "back": "Back", "cancel": "Cancel", "certificate": "Certificate", "certificateKeyNo": "Certificate No/ Key No", "certificatePreview": "Certificate Preview", "pleaseTryAgain": "Sorry, something went wrong. Please try again later.", "permissionDenied": "Permission Denied", "unAuthorized": "Un-Authorized", "newApplication": "New Application", "newApplications": "New Applications", "higherSecondary": "Higher Secondary (HSS)", "underGraduate": "Under Graduation (UG)", "masterGraduation": "Post Graduation (PG)", "defaultSubtitle": "Always stay updated in your student portal", "hssSubtitle": "For students learning in class 11", "ugSubtitle": "For undergraduate degree programs and courses", "pgSubtitle": "For postgraduate and master's degree programs", "apply": "Apply", "importantNote": "Important Note", "familyIncomeCriteria": "Family income must be ≤ ₹2.5 lakhs per annum to be eligible for the scholarship", "welcome": "Welcome!", "pageNotFound": "Page Not Found", "permanentAddress": "Permanent Address", "firstName": "First Name", "middleName": "Middle Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "gender": "Gender", "aadhaarNumber": "<PERSON><PERSON><PERSON><PERSON>", "houseNumber": "House Number & Name", "streetLocality": "Street/Locality", "cityTown": "City/Town", "district": "District", "state": "State", "pincode": "Pincode", "mobileNumber": "Mobile Number", "isResident": "Are you a Keralite?", "isNriParent": "Is this applicant a ward of an NRI parent?", "isDifferentlyAbled": "Are you a Differently Abled Candidate (60% disability or more)?", "previous": "Previous", "save": "Save", "submitApplication": "Submit Application", "nextStep": "Next Step", "emailId": "Email ID", "pravasiIdCardNumber": "Pravasi ID Card Number", "percentageOfDisability": "Percentage of Disability", "additionalDetails": "Additional Details (If applicable)", "isOrphan": "Are you an Orphan?", "isSingleParentHousehold": "Do you belong to a single-parent household with a bedridden parent?", "isFromSingleParentHousehold": "Are you from a Single Parent Household?", "areBothParentsBedridden": "Are both parents bedridden/terminally ill?", "hasRepresentedAtStateLevel": "Have you represented at the State Level in Sports/Arts?", "parentGuardianDetails": "Parent / Guardian Details", "applicantCareStatus": "Applicant is under the care of", "parentName": "Parent Name", "parent": "Parent", "father": "Father", "mother": "Mother", "name": "Name", "select": "Select", "enterName": "Enter name", "enterHere": "Enter here", "currentCareProvider": "Current Care Provider", "guardian": "Guardian", "guardianDetails": "Guardian Details", "institution": "Institution", "institutionDetails": "Institution Details", "nameOfInstitution": "Name of Institution", "institutionRegistrationNumber": "Institution Registration Number", "institutionContactNumber": "Institution Contact Number", "relationshipToApplicant": "Relationship to Applicant", "contactNumber": "Contact Number", "financialDetails": "Financial Details", "annualFamilyIncome": "Annual Family Income (in ₹)", "incomeCertificateIssuedBy": "Income Certificate Issued by", "incomeCertificateNo": "Income Certificate No", "certificateIssuedDate": "Certificate Issued Date", "sourceOfIncome": "Source(s) of Income", "academicDetails": "Academic Details", "class10BoardExam": "Class 10 Board Exam", "board": "Board", "grade": "Grade/Percentage", "enterGradePercentage": "Enter your grade/percentage", "institutionName": "Institution Name", "institutionNamePlaceholder": "Institution name", "institutionType": "Institution Type", "institutionLocation": "Institution Location", "yearofCompletion": "Year of Completion", "stateofInstitution": "State of Institution", "districtofInstitution": "District of Institution", "currentCourseDetails": "Current Course Details", "pleaseSelectOption": "Please Select an Option", "government": "Government", "aided": "Aided", "selfFinanced": "Self-Financed", "others": "Others", "selectInstitutionName": "Select the institution name", "courseMode": "Course Mode", "regular": "Regular", "correspondence": "Correspondence", "academicYear": "Academic Year", "dateOfAdmission": "Date of Admission", "bankDetails": "Bank Details", "bankAccountRequirement": "To apply for the scholarship, you must have a bank account in your own name. Please enter your bank account details below.", "accountHolderName": "Name of the Account Holder", "accountNumber": "Account Number", "enterAccountNumber": "Enter account number", "bankName": "Bank Name", "branch": "Branch", "ifsc": "IFSC", "documentUpload": "Document Upload", "documentGuidelinesTitle": "Document Guidelines", "documentGuidelinesMessage": "All files should be in PDF or JPEG format and under 2MB each. Ensure documents are clear and readable.", "aadhaarCard": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarDescription": "Government issued identity proof", "passportPhoto": "Passport Size Photograph", "passportDescription": "Recent passport-size photograph", "sslcMarksheet": "STATE/CBSE/ICSE SSLC Marksheet", "sslcMarksheetDescription": "For HSS applicants only", "ugMarksheet": "UG Marksheet", "ugMarksheetDescription": "For PG applicants only", "scoreCard": "Score Card All India Level Exam", "scoreCardDescription": "For PG applicants only", "incomeCertificate": "Income Certificate", "incomeCertificateDescription": "Official family income certificate issued in parent/guardian name", "fileFormatRequirements": "PDF or JPEG • Max 2MB", "aadhaarRequired": "<PERSON><PERSON><PERSON><PERSON> card is required", "passportPhotoRequired": "Passport photo is required", "marksheetRequired": "SSLC marksheet is required for HSS applicants", "ugMarksheetRequired": "UG marksheet is required for PG applicants", "scoreCardRequired": "Score card is required for PG applicants", "incomeCertificateRequired": "Income certificate is required", "fileSizeExceeded": "File size must be less than 2MB", "invalidFileFormat": "Only PDF and JPEG files are allowed", "specialCertificateRequired": "Special certificate is required for your status", "bankPassbookRequired": "Bank passbook is required", "rationCardRequired": "Ration card copy is required", "specialCertificate": "Orphan/Single Parent/Medical Certificate", "specialCertificateDescription": "Required based on selected status", "achievementCertificate": "Sports/Arts Achievement Certificate", "achievementCertificateDescription": "If applicable", "bankPassbook": "Bank Passbook", "bankPassbookDescription": "For account verification", "rationCard": "Ration Card Copy", "rationCardDescription": "For account verification", "otpResentTitle": "OTP Resent", "otpResentMessage": "A new OTP has been sent to your registered email/phone", "goBack": "Go back", "createAccountTitle": "Create Your Account", "registrationCompletionText": "To complete your registration, please fill in all the fields below", "otpVerificationInstruction": "Please complete the OTP verification by entering the code sent to your registered email ID or phone number", "otpRequired": "OTP is required", "otpLengthError": "OTP must be 6 digits", "enterVerificationCode": "Enter verification", "resendOTPButton": "Resend OTP", "verifyingText": "Verifying...", "nextButton": "Next", "signInLink": "Sign in", "successIconAlt": "Success tick icon", "eligibilityAlert": "Eligibility Alert", "keralaOriginRequired": "Only candidates of Kerala origin are eligible for this scholarship", "accountCreatedTitle": "User Account Created", "accountCreatedMessage": "Signup successful! Use your mobile number or email ID to log in.", "goToLoginButton": "Go to Login", "mobileNo": "Mobile Number", "getOTP": "Get OTP", "resendOTP": "Resend OTP", "verify": "Verify", "enter": "Enter", "otp": "OTP", "email": "Email", "invalidType": "Please enter a valid {{type}}", "familyCircumstances": "Family Circumstances", "aadhaarVerifiedSuccessfully": "Verified Successfully", "aadhaarInvalidNumber": "<PERSON><PERSON><PERSON>", "aadhaarVerificationFailed": "Verification Failed", "reviewSubmit": "Review & Submit", "personalDetails": "Personal Details", "CurrentCourseDetails": "Current Course Details", "documentsUpload": "Documents Upload", "parentDetails": "Parent Details", "financialInformation": "Financial Information", "ifscCode": "IFSC Code", "branchName": "Branch Name", "currentCourse": "Current Course", "yearOfStudy": "Year of Study", "cgpaPercentage": "CGPA/Percentage", "rationCardNumber": "Ration Card Number", "country": "Country", "disabilityStatus": "Disability Status", "disabilityPercentage": "Disability Percentage", "applicantUnderCareOf": "Applicant is under the care of", "relation": "Relation", "submit": "Submit", "confirm": "Confirm", "confirmApplication": "Confirm Application", "submitApplicationConfirmMessage": "By clicking 'CONFIRMATION', you are submitting your {{scholarshipType}} NORKA Scholarship application. Please note that once submitted, the application cannot be edited or deleted. Are you sure you want to proceed?", "applicationSubmittedSuccessfully": "Application submitted successfully!", "applicationSubmissionFailed": "Failed to submit application. Please try again.", "parentGuardianInfo": "Parent/Guardian Info", "documents": "Documents", "class10BoardExamDetails": "Class 10 Board Exam Details", "higherSecondaryDetails": "Higher Secondary Details", "underGraduationDetails": "Under Graduation Details", "gradePercentage": "Grade/Percentage", "yearOfCompletion": "Year of Completion", "stateOfInstitution": "State of Institution", "districtOfInstitution": "District of Institution", "courseName": "Course Name", "stream": "Stream", "competitiveExam": "Competitive Exam (All India Level)", "gradingSystem": "Grading System", "marksObtained": "Marks Obtained", "totalMarks": "Total Marks", "percentage": "Percentage", "selectCourseName": "Select Course Name", "instructionType": "Instruction Type", "instructionLocation": "Instruction Location", "instructionName": "Instruction Name", "mark": "<PERSON>", "cgpa": "CGPA", "enterPercentage": "Enter Percentage", "hsBoard": "Higher Secondary Board", "hsGradePercentage": "Higher Secondary Grade/Percentage", "hsInstitutionName": "Higher Secondary Institution Name", "hsYearOfCompletion": "Higher Secondary Year of Completion", "hsStateOfInstitution": "Higher Secondary State of Institution", "hsDistrictOfInstitution": "Higher Secondary District of Institution", "underGradCourseName": "Under Graduate Course Name", "underGradYearOfCompletion": "Under Graduate Year of Completion", "underGradStateOfInstitution": "Under Graduate State of Institution", "underGradDistrictOfInstitution": "Under Graduate District of Institution", "enterInstitutionName": "Enter Institution Name", "currentInstitutionType": "Current Institution Type", "next": "Next", "upload": "Upload", "changeFile": "Change File", "saving": "Saving...", "slNo": "Sl No", "studentName": "Student Name", "scholarshipType": "Scholarship Type", "appliedDate": "Applied Date", "action": "Action", "edit": "Edit", "view": "View", "draft": "Draft", "approved": "Approved", "applied": "Applied", "processing": "Processing", "myApplications": "My Applications", "search": "Search", "filter": "Filter", "sendOtpSuccess": "O<PERSON> Successfully", "verifyOtpSuccess": "OTP Verified Successfully", "failedToSendOtp": "Failed to send OTP", "yourName": "Your Name", "institutionAddress": "Institution Address", "userName": "User Name", "norkaId": "Norka ID", "password": "Password", "confirmPassword": "Confirm Password", "alreadyRegistered": "Already registered?", "createAccount": "Create Your Account", "consentTitle": "Aadhaar Verification Consent", "consentMessage": "This application requires <PERSON><PERSON><PERSON><PERSON> verification for both the student and their parents/guardians to ensure the authenticity of the information provided. Your Aadhaar details will be used solely for verification purposes and will be handled in accordance with data privacy regulations.", "consentCheckboxLabel": "I agree to provide <PERSON><PERSON><PERSON><PERSON> details for verification of the student and parent/guardian information", "agreeAndContinue": "Agree and Continue", "consentRequired": "You must agree to the consent terms to proceed", "noDocumentsFound": "No Documents Found", "documentsWillAppearHere": "Documents will appear here once uploaded", "noAadhaarVaultId": "Kindly verify your <PERSON><PERSON><PERSON><PERSON> Number before you submit the form", "error": "Error", "aadhaarNameMismatch": "The name entered does not match the name on the Aadhaar. Please ensure the name matches exactly as per Aadhaar records."}