import { flow } from 'lodash-es';
import { STATE_REDUCER_KEY } from './constants';

const getApplicationDetailsKey = (state) => state[STATE_REDUCER_KEY.SLICE];

const getCurrentStepData = (state) => state?.currentStep;
export const getCurrentStep = flow(getApplicationDetailsKey, getCurrentStepData);

const getFormDataKey = (state) => state?.formData;
export const getFormData = flow(getApplicationDetailsKey, getFormDataKey);

const getApplicationIdKey = (state) => state?.applicationId;
export const getApplicationId = flow(getApplicationDetailsKey, getApplicationIdKey);

const getFormIdsKey = (state) => state?.formIds;
export const getFormIds = flow(getApplicationDetailsKey, getFormIdsKey);

const getParentGuardianIdKey = (state) => state?.formIds?.parentGuardianId;
export const getParentGuardianId = flow(getApplicationDetailsKey, getParentGuardianIdKey);

const getBankDetailsIdKey = (state) => state?.formIds?.bankDetailsId;
export const getBankDetailsId = flow(getApplicationDetailsKey, getBankDetailsIdKey);

const getAcademicDetailsIdKey = (state) => state?.formIds?.academicDetailsId;
export const getAcademicDetailsId = flow(getApplicationDetailsKey, getAcademicDetailsIdKey);

const getDocumentsIdKey = (state) => state?.formIds?.documentsId;
export const getDocumentsId = flow(getApplicationDetailsKey, getDocumentsIdKey);
