name: Deploy Frontend Service

on:
  push:
    branches:
      - develop

jobs:
  deploy-frontend:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout Code
      - name: Checkout Code
        uses: actions/checkout@v4

      # Step 2: SSH into EC2 and Deploy
      - name: Build and Restart Frontend on EC2
        uses: appleboy/ssh-action@v1.2.0
        with:
          host: ${{ secrets.EC2_HOST }}
          username: ${{ secrets.EC2_USER }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            # Navigate to the correct project directory
            cd /home/<USER>/norka-frontend-service

            # Install Node.js and npm if not present
            if ! command -v node &> /dev/null; then
              echo "Installing Node.js and npm..."
              curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
              sudo apt install -y nodejs
            fi

            # Install pnpm globally if not present
            if ! command -v pnpm &> /dev/null; then
              echo "Installing pnpm..."
              npm install -g pnpm
            fi

            # Install PM2 globally if not present
            if ! command -v pm2 &> /dev/null; then
              echo "Installing PM2..."
              npm install -g pm2
            fi

            # Pull the latest code from the develop branch
            git reset --hard
            git checkout develop
            git pull origin develop

             # Choose env: dev, staging, or production
             ENV=dev
             echo "Using .env.$ENV"
             cp .env.$ENV .env

            # Install dependencies
            export VITE_BASE_URL=/
            pnpm install

            # Build the frontend application
            NODE_OPTIONS="--max-old-space-size=4096" pnpm run build

            # Restart the application with PM2
            pm2 delete norka-fe || true
            pm2 serve dist 3001 --name norka-fe --spa --env $ENV

            echo "Frontend application pulled, built, and restarted successfully."