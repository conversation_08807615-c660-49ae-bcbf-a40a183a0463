import * as yup from 'yup';
import { t } from 'i18next';
import { YES_OR_NO } from 'pages/common/constant';
import {
  AADHAAR, EMAIL, MOBILE, PINCODE
} from 'common/regex';
import { STATE } from 'common/constants';

export const applicantDetailsSchema = yup.object().shape({
  // Personal Details
  firstName: yup
    .string()
    .required(t('isRequired', { type: t('firstName') }))
    .min(2, t('mustBeAtLeast', { type: t('firstName'), count: 2, unit: 'characters' }))
    .max(50, t('fieldMaxLength', { field: t('firstName'), max: 50 })),

  middleName: yup
    .string()
    .nullable()
    .notRequired()
    .max(50, t('fieldMaxLength', { field: t('middleName'), max: 50 })),

  lastName: yup
    .string()
    .nullable()
    .notRequired()
    .max(50, t('fieldMaxLength', { field: t('lastName'), max: 50 })),

  dateOfBirth: yup
    .date()
    .required(t('isRequired', { type: t('dateOfBirth') }))
    .max(new Date(), t('fieldFutureDate', { field: t('dateOfBirth') }))
    .test('age', t('fieldMinAge', { age: 16 }), (value) => {
      if (!value) return false;
      const today = new Date();
      const birthDate = new Date(value);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 16;
      }
      return age >= 16;
    }),

  gender: yup
    .string()
    .required(t('fieldSelectOption', { field: t('gender') })),

  aadhaarNumber: yup
    .string()
    .required(t('isRequired', { type: t('aadhaarNumber') }))
    .matches(AADHAAR, t('fieldValidFormat', { field: t('aadhaarNumber') })),

  // Permanent Address
  houseNumber: yup
    .string()
    .required(t('isRequired', { type: t('houseNumber') }))
    .max(100, t('fieldMaxLength', { field: t('houseNumber'), max: 100 })),

  streetLocality: yup
    .string()
    .required(t('isRequired', { type: t('streetLocality') }))
    .max(200, t('fieldMaxLength', { field: t('streetLocality'), max: 200 })),

  cityTown: yup
    .string()
    .required(t('isRequired', { type: t('cityTown') }))
    .max(100, t('fieldMaxLength', { field: t('cityTown'), max: 100 })),

  district: yup
    .string()
    .required(t('fieldSelectOption', { field: t('district') })),

  state: yup
    .string()
    .required(t('fieldSelectOption', { field: t('state') })),

  pincode: yup
    .string()
    .required(t('isRequired', { type: t('pincode') }))
    .matches(PINCODE, t('fieldExactLength', { field: t('pincode'), length: 6 })),

  mobileNumber: yup
    .string()
    .transform((value) => (value ? String(value) : ''))
    .required(t('isRequired', { type: t('mobileNumber') }))
    .matches(MOBILE, t('fieldValidMobile')),

  emailId: yup
    .string()
    .transform((value) => (value ? String(value) : ''))
    .required(t('isRequired', { type: t('emailId') }))
    .test(
      'emailFormat',
      t('fieldValidFormat', { field: t('emailId') }),
      (value) => !value || EMAIL.test(value)
    ),

  // Questions
  isResident: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isResident') })),

  isNriParent: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isNriParent') })),

  pravasiIdCardNumber: yup
    .string()
    .when('isNriParent', {
      is: YES_OR_NO.YES,
      then: (schema) => schema.required(t('isRequired', { type: t('pravasiIdCardNumber') })),
      otherwise: (schema) => schema.notRequired()
    }),

  isDifferentlyAbled: yup
    .string()
    .required(t('fieldSelectOption', { field: t('isDifferentlyAbled') })),

  percentageOfDisability: yup
    .number()
    .nullable()
    .transform((value, originalValue) => {
      // Transform empty string to null to avoid NaN
      return originalValue === '' ? null : value;
    })
    .when('isDifferentlyAbled', {
      is: YES_OR_NO.YES,
      then: (schema) => schema
        .required(t('isRequired', { type: t('percentageOfDisability') }))
        .min(60, t('valueMustBeInBetween', {
          type: t('percentageOfDisability'), start: 60, end: 100, unit: '%'
        }))
        .max(100, t('valueMustBeInBetween', {
          type: t('percentageOfDisability'), start: 60, end: 100, unit: '%'
        })),
      otherwise: (schema) => schema.notRequired()
    })
});

export const combinedApplicantDetailsSchema = applicantDetailsSchema;

export const APPLICANT_DETAILS_DEFAULT_VALUES = {
  firstName: '',
  middleName: '',
  lastName: '',
  dateOfBirth: null,
  gender: null,
  aadhaarNumber: '',
  houseNumber: '',
  streetLocality: '',
  cityTown: '',
  district: '',
  state: STATE.id,
  pincode: '',
  mobileNumber: '',
  emailId: '',
  isResident: '',
  isNriParent: '',
  pravasiIdCardNumber: '',
  isDifferentlyAbled: '',
  percentageOfDisability: null
};

// Additional Details (If applicable) Default Values
export const ADDITIONAL_DETAILS_DEFAULT_VALUES = {
  familyCircumstances: '',
  hasRepresentedAtStateLevel: YES_OR_NO.NO
};

export const COMBINED_DEFAULT_VALUES = {
  ...APPLICANT_DETAILS_DEFAULT_VALUES,
  ...ADDITIONAL_DETAILS_DEFAULT_VALUES
};
