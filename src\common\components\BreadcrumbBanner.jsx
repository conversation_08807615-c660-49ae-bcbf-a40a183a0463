import { ApplicationIcon, ArrowIcon, Pencil } from 'assets/svg';
import { BannerCorner } from 'assets/images';
import { useNavigate } from 'react-router-dom';
import { getScholarshipType } from 'pages/common/selectors';
import { useSelector } from 'react-redux';
import { t } from 'i18next';
import {
  Box, Flex, Text, Icon,
  Image
} from 'common/components';

// Reusable components
const BackArrow = ({ onClick }) => (
  <Icon
    as={ArrowIcon}
    color="white"
    cursor="pointer"
    boxSize={3}
    onClick={onClick}
    flexShrink={0}
    opacity={0.9}
  />
);

const ScholarshipTag = ({ type, mobile = false }) => (
  <Text
    fontSize={mobile ? 'lg' : '2xl'}
    fontWeight="bold"
    color="white"
  >
    {t(type)}
  </Text>
);

const Subtitle = ({ text }) => (
  <Text fontSize="sm" color="white" opacity={0.9}>
    {t(text)}
  </Text>
);

const BreadcrumbBanner = ({
  subtitle = 'defaultSubtitle',
  onBack = () => {}
}) => {
  const navigate = useNavigate();
  const scholarshipType = useSelector(getScholarshipType);

  const handleBack = () => onBack() || navigate(-1);

  return (
    <Box
      bg="primary.500"
      borderRadius="lg"
      w="100%"
      overflow="hidden"
      mb={4}
      position="relative"
      minH={{ base: '100px', md: '150px' }}
      h={{ md: '150px' }}
    >
      {/* Mobile Layout */}
      <Box
        display={{ base: 'block', md: 'none' }}
        p={4}
        minH="100px"
        position="relative"
      >
        <Box
          bg="rgba(255, 255, 255, 0.15)"
          borderRadius="lg"
          px={3}
          py={2}
          mb={2}
          display="inline-flex"
          alignItems="center"
          gap={2}
          backdropFilter="blur(10px)"
          border="1px solid rgba(255, 255, 255, 0.2)"
        >
          <BackArrow onClick={handleBack} />
          <Flex align="center" gap={1}>
            <ApplicationIcon />
            <Text
              fontWeight="semibold"
              fontSize="sm"
              color="white"
              whiteSpace="nowrap"
              opacity={0.9}
            >
              {t('newApplication')}
            </Text>
          </Flex>
        </Box>
        <Text
          fontSize="lg"
          fontWeight="bold"
          color="white"
          mb={1}
        >
          <ScholarshipTag type={scholarshipType} mobile />
        </Text>
        <Subtitle text={subtitle} />
      </Box>

      {/* Desktop Layout */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        align="center"
        justify="space-between"
        display={{ base: 'none', md: 'flex' }}
        h={{ base: '100px', md: '150px' }}
        pl={6}
        pr={0}
        py={4}
        position="relative"
      >
        <Box maxW="60%" color="white">
          <Box
            bg="rgba(255, 255, 255, 0.15)"
            borderRadius="lg"
            px={2}
            py={2}
            mb={2}
            display="inline-flex"
            alignItems="center"
            gap={2}
            backdropFilter="blur(10px)"
            border="1px solid rgba(255, 255, 255, 0.2)"
          >
            <BackArrow onClick={handleBack} />
            <Flex align="center" gap={2}>
              <ApplicationIcon />
              <Text
                fontWeight="500"
                fontSize="sm"
                color="primary.50"
                opacity={0.9}
              >
                {t('newApplication')}
              </Text>
            </Flex>
          </Box>
          <Text
            fontSize="2xl"
            fontWeight="bold"
            color="white"
            mb={2}
          >
            <ScholarshipTag type={scholarshipType} />
          </Text>
          <Subtitle text={subtitle} />
        </Box>

        {/* Right Section: Images - Positioned absolutely to remove padding */}
        <Box
          position="absolute"
          right={0}
          top={0}
          bottom={0}
          width={{ base: '100%', md: '50%' }}
          display={{ base: 'none', md: 'block' }}
          overflow="hidden"
        >
          {/* Pencil Image - Positioned to overlap slightly */}
          <Image
            src={Pencil}
            alt="Graduation Cap"
            position="absolute"
            right="180px"
            top="0"
            bottom="0"
            height="100%"
            width="auto"
            objectFit="contain"
            zIndex={2}
          />
          {/* Corner Image - Background positioned */}
          <Image
            src={BannerCorner}
            alt="Books"
            position="absolute"
            right="0"
            top="0"
            bottom="0"
            height="100%"
            width="auto"
            objectFit="contain"
            zIndex={1}
          />
        </Box>
      </Flex>
    </Box>
  );
};

export default BreadcrumbBanner;
