import { configureStore } from '@reduxjs/toolkit';
import { apiSlices, sliceReducers } from 'app/rootReducer';
import logger from 'redux-logger';
import { rtkQueryErrorHandler } from 'utils/http';

const reducers = { ...sliceReducers };
const middlewareList = [];

apiSlices.forEach((api) => {
  reducers[api.reducerPath] = api.reducer;
  middlewareList.push(api.middleware);
});

if (import.meta.env.MODE === 'development') {
  middlewareList.push(logger);
}

export const store = configureStore({
  devTools: true,
  reducer: reducers,
  middleware: (getDefaultMiddleware) => getDefaultMiddleware()
    .concat(...middlewareList)
    .concat(rtkQueryErrorHandler)
});
