import { createSlice } from '@reduxjs/toolkit';
import { _ } from 'utils/lodash';
import { STATE_REDUCER_KEY } from './constant';

const initialState = {
  documents: {
    DEFAULT_FILE_ID: {}
  }
};

const commonSlice = createSlice({
  name: STATE_REDUCER_KEY.SLICE,
  initialState,
  reducers: {
    clearAll: () => initialState,
    setDocuments: (state, { payload }) => {
      _.set(state, 'documents', { ...state.documents, ...payload });
    },
    clearDocuments: (state) => {
      _.set(state, 'documents', {});
    }
  }
});

export const { actions, reducer } = commonSlice;
