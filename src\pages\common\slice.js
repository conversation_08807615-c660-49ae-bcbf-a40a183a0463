import { createSlice } from '@reduxjs/toolkit';
import { STATE } from 'common/constants';
import { _ } from 'utils/lodash';
import { STATE_REDUCER_KEY } from './constant';

const initialState = {
  customAlert: {
    open: false,
    variant: 'info',
    message: '',
    title: ''
  },
  customToast: {
    open: false,
    variant: 'info',
    message: '',
    title: ''
  },
  commonConfig: {
    lang: 'en',
    stateCode: STATE.code
  },
  navigate: {
    isSameModule: true,
    active: false,
    to: ''
  },
  scholarshipType: null,
  scholarshipTypeId: null,
  otp: {},
  aadhaarVerificationConsent: false
};

const commonSlice = createSlice({
  name: STATE_REDUCER_KEY.SLICE,
  initialState,
  reducers: {
    clearAll: () => initialState,
    navigateTo: (state, { payload }) => {
      _.set(state, 'navigate', { active: true, ...payload });
    },
    disableNavigate: (state) => {
      _.set(state, 'navigate', initialState.navigate);
    },
    setCommonAlert: (state, { payload }) => {
      _.set(state, 'customAlert', payload);
    },
    setCustomToast: (state, { payload }) => {
      _.set(state, 'customToast', payload);
    },
    setScholarshipType: (state, { payload }) => {
      _.set(state, 'scholarshipType', payload);
    },
    setScholarshipTypeId: (state, { payload }) => {
      _.set(state, 'scholarshipTypeId', payload);
    },
    setOtpState: (state, { payload }) => {
      const { key, data } = payload;
      _.set(state, `otp.${key}`, { ..._.get(state, `otp.${key}`, {}), ...data });
    },
    clearOtpState: (state, { payload }) => {
      const { key } = payload;
      const newOtp = { ...state.otp };
      delete newOtp[key];
      _.set(state, 'otp', newOtp);
    },
    clearAllOtpState: (state) => {
      _.set(state, 'otp', {});
    },
    setAadhaarVerificationConsent: (state, { payload }) => {
      _.set(state, 'aadhaarVerificationConsent', payload);
    },
    clearConsent: (state) => {
      _.set(state, 'aadhaarVerificationConsent', initialState.aadhaarVerificationConsent);
    }
  }
});

export const { actions, reducer } = commonSlice;
