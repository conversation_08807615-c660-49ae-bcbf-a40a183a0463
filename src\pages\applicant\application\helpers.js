import { YES_OR_NO } from 'pages/common/constant';
import { formatDateWithDayjs } from 'utils/date';
import { selectorWithKey } from 'utils/common';
import { _ } from 'utils/lodash';
import { FAMILY_CIRCUMSTANCES, CARE_STATUS, CURRENT_CARE_PROVIDER } from './constants';

const transformHelpers = {
  toBool: (value) => value === YES_OR_NO.YES,
  parseInteger: (value) => {
    if (!value) return null;
    const parsed = parseInt(value, 10);
    return Number.isNaN(parsed) ? null : parsed;
  }
};

/**
 * Get uidVault from OTP verification state for a given Aadhaar number
 * @param {string} aadhaarNumber - The Aadhaar number to get uidVault for
 * @param {Object} otpState - The OTP state from Redux store
 * @returns {string|null} - The uidVault if available, otherwise null
 */
const getUidVaultFromOtpState = (aadhaarNumber, otpState) => {
  if (!aadhaarNumber || !otpState) return null;

  const aadhaarOtpData = selectorWithKey(otpState, aadhaarNumber) || {};
  const verificationData = aadhaarOtpData.verificationData || {};

  return _.get(verificationData, 'uidVault') || null;
};

/**
 * Get the name associated with the given Aadhaar number from OTP verification state
 * @param {string} aadhaarNumber - The Aadhaar number to get the name for
 * @param {Object} otpState - The OTP state from Redux store
 * @returns {string|null} - The name associated with the Aadhaar number, or null if not available
 */
export const getAadhaarNameFromOtpState = (aadhaarNumber, otpState) => {
  if (!aadhaarNumber || !otpState) return null;

  const aadhaarOtpData = selectorWithKey(otpState, aadhaarNumber) || {};
  return _.get(aadhaarOtpData.verificationData, 'name') || null;
};

const getFamilyCircumstancesFlags = (familyCircumstances) => {
  const flags = {
    orphan: false,
    singleParentAndBedridden: false,
    singleParent: false,
    bothParentsAndBedridden: false
  };

  if (!familyCircumstances) return flags;

  switch (familyCircumstances) {
    case FAMILY_CIRCUMSTANCES.ORPHAN:
      flags.orphan = true;
      break;
    case FAMILY_CIRCUMSTANCES.SINGLE_PARENT_BEDRIDDEN:
      flags.singleParentAndBedridden = true;
      break;
    case FAMILY_CIRCUMSTANCES.SINGLE_PARENT_HOUSEHOLD:
      flags.singleParent = true;
      break;
    case FAMILY_CIRCUMSTANCES.BOTH_PARENTS_BEDRIDDEN:
      flags.bothParentsAndBedridden = true;
      break;
    default:
      break;
  }

  return flags;
};

const transformFormData = (formData, scholarshipTypeId, otpState = null) => {
  const {
    toBool, parseInteger
  } = transformHelpers;
  const familyFlags = getFamilyCircumstancesFlags(formData.familyCircumstances);

  // Get uidVault from OTP state if available, otherwise use aadhaarNumber
  const uidVault = getUidVaultFromOtpState(formData.aadhaarNumber, otpState);

  return {
    firstName: formData.firstName ?? null,
    middleName: formData.middleName ?? null,
    lastName: formData.lastName ?? null,
    dateOfBirth: formatDateWithDayjs(formData.dateOfBirth),
    genderId: formData.gender,
    genderName: formData.gender,
    aadhaarVaultNo: uidVault ?? null,
    houseNoName: formData.houseNumber ?? null,
    streetLocality: formData.streetLocality ?? null,
    cityTown: formData.cityTown ?? null,
    districtId: formData.district,
    districtName: formData.district,
    stateId: formData.state,
    pincode: parseInteger(formData.pincode),
    contactNumber: formData.mobileNumber ?? null,
    emailId: formData.emailId ?? null,
    keralite: toBool(formData.isResident),
    nriParent: toBool(formData.isNriParent),
    pravasiIdCardNumber: formData.pravasiIdCardNumber ?? null,
    studentDisable: toBool(formData.isDifferentlyAbled),
    disabilityPercentage: formData.percentageOfDisability ?? null,
    ...familyFlags,
    sportsArtsAchievement: toBool(formData.hasRepresentedAtStateLevel) ?? null,
    educationQualificationId: scholarshipTypeId
  };
};

const transformBankDetails = (formData) => {
  return {
    accountHolderName: formData.accountHolderName,
    accountNumber: formData.accountNumber,
    bankName: formData.bankName,
    branchName: formData.branch,
    ifscCode: formData.ifsc
  };
};

/**
 * Transform form data to API payload format for parent/guardian details
 * @param {Object} formData - The form data from react-hook-form
 * @param {string} applicationId - The application ID
 * @param {Object} otpState - The OTP state from Redux store (optional)
 * @returns {Object} - Transformed payload for API
 */
const transformParentDetailsToPayload = (formData, applicationId, otpState = null) => {
  const { applicantCareStatus, currentCareProvider } = formData;

  const payload = {
    applicationId,
    parentOrGuardian: applicantCareStatus,
    guardian: currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN,
    institution: currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION,
    annualIncome: formData.annualFamilyIncome ?? 0,
    incomeCertificateNumber: formData.incomeCertificateNo ?? null,
    certificateIssuedBy: formData.incomeCertificateIssuedBy ?? null,
    certificateIssuedDate: formatDateWithDayjs(formData.certificateIssuedDate) ?? null
  };

  if (applicantCareStatus === CARE_STATUS.PARENTS) {
    const fatherUidVault = getUidVaultFromOtpState(formData.fatherAadhaarNumber, otpState);
    const motherUidVault = getUidVaultFromOtpState(formData.motherAadhaarNumber, otpState);

    payload.fatherName = formData.fatherName ?? null;
    payload.fatherContactNumber = formData.fatherContactNumber ?? null;
    payload.fatherAadhaarVaultNo = fatherUidVault ?? null;
    payload.motherName = formData.motherName ?? null;
    payload.motherContactNumber = formData.motherContactNumber ?? null;
    payload.motherAadhaarVaultNo = motherUidVault ?? null;
  } else if (applicantCareStatus === CARE_STATUS.SINGLE_PARENT) {
    const parentUidVault = getUidVaultFromOtpState(formData.aadhaarNumber, otpState);

    if (formData.relationshipToApplicant === 'FATHER') {
      payload.fatherName = formData.parentName ?? null;
      payload.fatherContactNumber = formData.contactNumber ?? null;
      payload.fatherAadhaarVaultNo = parentUidVault ?? null;
    } else {
      payload.motherName = formData.parentName ?? null;
      payload.motherContactNumber = formData.contactNumber ?? null;
      payload.motherAadhaarVaultNo = parentUidVault ?? null;
    }
  } else if (applicantCareStatus === CARE_STATUS.ORPHAN) {
    if (currentCareProvider === CURRENT_CARE_PROVIDER.GUARDIAN) {
      const guardianUidVault = getUidVaultFromOtpState(formData.guardianAadhaarNumber, otpState);

      payload.guardianName = formData.guardianName ?? null;
      payload.guardianRelation = formData.guardianRelationshipToApplicant ?? null;
      payload.guardianContactNumber = formData.guardianContactNumber ?? null;
      payload.guardianAadhaarVaultNo = guardianUidVault ?? null;
    } else if (currentCareProvider === CURRENT_CARE_PROVIDER.INSTITUTION) {
      payload.institutionName = formData.institutionName ?? null;
      payload.institutionRegNumber = formData.institutionRegistrationNumber ?? null;
      payload.institutionContactNumber = formData.institutionContactNumber ?? null;
      payload.institutionAddress = formData.institutionAddress ?? null;
    }
  }

  return payload;
};

export { transformFormData, transformParentDetailsToPayload, transformBankDetails };
