import colors from 'theme/foundations/colors';

export const selectStyle = {
  container: (base) => ({
    ...base,
    width: '100%',
    minHeight: '50px'
  }),
  control: (base) => ({
    ...base,
    width: '100%',
    minHeight: '50px',
    border: `1px solid ${colors.gray[300]}`,
    outline: 0,
    background: colors.white,
    boxShadow: 'none',
    borderRadius: '8px',
    cursor: 'pointer',
    padding: '0 16px',
    '&.Norka__control--menu-is-open, :hover': {
      border: `1px solid ${colors.tertiary[500]}`
    },
    '&.Norka__control--error': {
      border: `1px solid ${colors.red[500]} !important`
    }
  }),
  indicatorSeparator: () => ({
    display: 'none'
  }),
  placeholder: (base) => ({
    ...base,
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: '14px',
    lineHeight: '16px',
    opacity: '0.3'
  }),
  valueContainer: (base) => ({
    ...base,
    paddingLeft: 0
  }),
  indicatorsContainer: (base) => ({
    ...base,
    padding: '0',
    div: {
      padding: 0
    }
  }),
  menu: (base) => ({
    ...base,
    margin: '0',
    width: '90%',
    padding: '4px',
    left: '50%',
    transform: 'translateX(-50%)',
    boxShadow: '0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);',
    borderRadius: '4px',
    border: 0,
    outline: 0,
    zIndex: 1000,
    position: 'absolute',
    // This class is essential for functionality in other modules. Do not remove or modify.
    '&.Norka__menu': {}
  }),
  option: (base) => ({
    ...base,
    border: `1px solid ${colors.transparent}`,
    fontSize: '14px',
    fontWeight: 'normal',
    display: 'flex',
    columnGap: '6px',
    justifyContent: 'flex-start',
    color: colors.gray[700],
    alignItems: 'center',
    borderRadius: '4px',
    cursor: 'pointer',
    transition: 'all 150ms ease-in-out',
    ':hover, &.Norka__option--is-focused': {
      background: 'transparent',
      border: `1px solid ${colors.tertiary[500]}`
    },
    '&.Norka__option--is-selected': {
      border: `1px solid ${colors.transparent}`,
      background: colors.primary[50]
    },
    '&.Norka__option--is-disabled': {
      border: `1px solid ${colors.transparent}`,
      color: colors.gray[300]
    }
  }),
  menuPortal: (base) => ({
    ...base,
    zIndex: 1000,
    // This class is essential for functionality in other modules. Do not remove or modify.
    '&.Norka__menu-portal': {}
  })
};

const style = {
  '.dropdown-contain': {
    position: 'relative',
    width: '100%',
    minHeight: '50px',
    borderRadius: '8px',
    'p.form-label': {
      position: 'absolute',
      top: '-7px',
      background: `${colors.white} !important`,
      padding: '0 5px',
      left: '13px',
      fontSize: '13px',
      fontWeight: '400',
      lineHeight: '14px',
      color: `${colors.primary[900]}`,
      zIndex: 2
    },
    '&.has-left-icon .Norka__control': {
      paddingLeft: '50px'
    },

    '.custom-left-dropdown-content': {
      position: 'absolute',
      top: 0,
      bottom: 0,
      left: '20px',
      display: 'flex',
      alignItems: 'center',
      zIndex: 1
    }
  }
};

const component = {};

export default { style, component };
