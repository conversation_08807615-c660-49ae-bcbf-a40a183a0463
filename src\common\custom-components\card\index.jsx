import React from 'react';
import {
  Box, Image, Text, Button, VStack, useColorModeValue
} from 'common/components';

const ProgramCard = ({
  imageUrl = '',
  heading = '',
  subheading = '',
  buttonText = 'Apply',
  onButtonClick,
  maxWidth = '800px',
  borderRadius = '16px',
  ...props
}) => {
  const cardBg = useColorModeValue('white', 'gray.800');
  const cardShadow = useColorModeValue('0px 4px 12px rgba(0, 0, 0, 0.1)', '0px 4px 12px rgba(0, 0, 0, 0.3)');

  return (
    <Box
      maxW={maxWidth}
      bg={cardBg}
      borderRadius={borderRadius}
      overflow="hidden"
      boxShadow={cardShadow}
      transition="transform 0.2s, box-shadow 0.2s"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: useColorModeValue('0px 8px 20px rgba(0, 0, 0, 0.15)', '0px 8px 20px rgba(0, 0, 0, 0.4)')
      }}
      {...props}
    >
      {/* Image Section */}
      <Box position="relative" height="200px" overflow="hidden">
        <Image
          src={imageUrl}
          alt={heading}
          width="100%"
          height="100%"
          objectFit="cover"
          transition="transform 0.3s"
          _hover={{ transform: 'scale(1.05)' }}
        />
      </Box>

      {/* Content Section */}
      <VStack spacing={4} p={6} align="stretch">
        <VStack spacing={2} align="center">
          <Text
            fontSize="lg"
            fontWeight="semibold"
            color={useColorModeValue('gray.800', 'white')}
            lineHeight="1.3"
          >
            {heading}
          </Text>
          <Text
            fontSize="sm"
            color={useColorModeValue('gray.600', 'gray.300')}
            lineHeight="1.4"
          >
            {subheading}
          </Text>
        </VStack>

        {/* Apply Button */}
        <Button
          variant="primary_light"
          size="md"
          width="100%"
          fontWeight="semibold"
          onClick={onButtonClick}
          _hover={{
            bg: 'secondary.500',
            color: 'white'
          }}
        >
          {buttonText}
        </Button>
      </VStack>
    </Box>
  );
};

export default ProgramCard;
