import { OTP_TYPES, AADHAAR_PROJECT, AADHAAR_MODULE } from 'common/constants';
import {
  useSendMobileOtpMutation,
  useSendEmailOtpMutation,
  useSendAadhaarOtpMutation,
  useVerifyOtpMutation
} from 'pages/common/api';
import { getOtp } from 'pages/common/selectors';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectorWithKey } from 'utils/common';
import { _ } from 'utils/lodash';
import CustomOtp from './CustomOtp';

const { MOBILE: MOBILE_TYPE, EMAIL: EMAIL_TYPE, AADHAAR: AADHAAR_TYPE } = OTP_TYPES;

const OtpInput = (props) => {
  const {
    value = '', name = 'aadhaarNumber', control, getValues, setValue, errors,
    getValue = (input) => input, onVerified = (input) => input, otpType = AADHAAR_TYPE, verified,
    verificationData = null,
    ...rest
  } = props;

  // Get current value from form if available, otherwise use prop value
  const currentValue = getValues ? getValues(name) : value;
  const [input, setInput] = useState(currentValue || '');

  // RTK Query mutations
  const [sendMobileOtp, { isLoading: mobileOtpLoading }] = useSendMobileOtpMutation();
  const [sendEmailOtp, { isLoading: emailOtpLoading }] = useSendEmailOtpMutation();
  const [sendAadhaarOtp, { isLoading: aadhaarOtpLoading }] = useSendAadhaarOtpMutation();
  const [verifyOtp, { isLoading: verifyOtpLoading }] = useVerifyOtpMutation();

  const otpState = selectorWithKey(useSelector(getOtp), input) || {};
  const {
    isVerified = false,
    otpSuccess = false,
    verificationData: stateVerificationData = null
  } = otpState;

  // Use RTK Query loading states directly
  const otpInProgress = mobileOtpLoading || emailOtpLoading || aadhaarOtpLoading;
  const verifyInProgress = verifyOtpLoading;

  // Update input when form value changes
  useEffect(() => {
    if (getValues) {
      const formValue = getValues(name);
      if (formValue !== input) {
        setInput(formValue || '');
      }
    }
  }, [getValues, name, input]);

  useEffect(() => {
    if (isVerified) {
      onVerified(input);
      // Update form to mark as verified
      if (setValue) {
        setValue(`${name}Verified`, true);
      }
    }
  }, [isVerified, input, onVerified, setValue, name]);

  const handleSendOtp = async (request) => {
    const inputValue = _.get(request, name);
    getValue(inputValue);
    setInput(inputValue);

    // Update form value if setValue is available
    if (setValue) {
      setValue(name, inputValue);
    }

    if (otpType === MOBILE_TYPE) {
      await sendMobileOtp({ mobile: inputValue });
    } else if (otpType === EMAIL_TYPE) {
      await sendEmailOtp({ email: inputValue });
    } else if (otpType === AADHAAR_TYPE) {
      await sendAadhaarOtp({
        aadhaarNo: inputValue,
        project: AADHAAR_PROJECT.KSMART,
        module: AADHAAR_MODULE.PENSION
      });
    }
  };

  const handleVerifyOtp = async (request) => {
    const inputValue = _.get(request, name);
    const otpValue = _.get(request, `${name}Otp`);

    const payload = { otp: otpValue };
    if (otpType === MOBILE_TYPE) {
      payload.mobile = inputValue;
    } else if (otpType === EMAIL_TYPE) {
      payload.email = inputValue;
    } else if (otpType === AADHAAR_TYPE) {
      payload.aadhaarNo = inputValue;
      payload.project = AADHAAR_PROJECT.KSMART;
      payload.module = AADHAAR_MODULE.PENSION;
      // Include txn from the stored OTP state
      const txn = _.get(otpState, 'txn');
      if (txn) {
        payload.txn = txn;
      }
    }

    await verifyOtp(payload);
  };

  return (
    <CustomOtp
      verified={isVerified || verified}
      control={control}
      errors={errors}
      getValues={getValues}
      setValue={setValue}
      onSend={handleSendOtp}
      onVerify={handleVerifyOtp}
      onReSend={handleSendOtp}
      otpInProgress={otpInProgress}
      verifyInProgress={verifyInProgress}
      otpSuccess={otpSuccess}
      otpType={otpType}
      value={value}
      name={name}
      isEditable={false}
      verificationData={stateVerificationData || verificationData}
      {...rest}
    />
  );
};

export default OtpInput;
