import { getNavigate } from 'pages/common/selectors';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { actions as commonActions } from 'pages/common/slice';

const Navigator = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const {
    to,
    options = {},
    active = false,
    isSameModule = true
  } = useSelector(getNavigate);

  useEffect(() => {
    if (active) {
      if (isSameModule) {
        navigate(to, options);
      } else {
        window.location.href = to;
      }
      dispatch(commonActions.disableNavigate());
    }
  }, [active]);
};

export default Navigator;
