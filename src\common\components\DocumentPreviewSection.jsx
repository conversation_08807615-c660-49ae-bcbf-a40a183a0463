import React, { useState } from 'react';
import {
  Box,
  Grid,
  GridItem,
  Text,
  VStack,
  HStack,
  Icon,
  IconButton,
  useDisclosure,
  Badge
} from 'common/components';
import { ExpandIcon, PDFIcon } from 'assets/svg';
import { DocumentModal } from 'common/custom-components';
import { t } from 'i18next';
import colors from 'theme/foundations/colors';

const DocumentCard = ({ document, onPreview, ...cardProps }) => {
  const isImage = document.type?.startsWith('image/');
  const isPDF = document.type === 'application/pdf';
  const isPNG = document.type === 'image/png';
  const isJPEG = document.type === 'image/jpeg' || document.type === 'image/jpg';

  const formatFileSize = (bytes) => {
    if (!bytes) return 'Unknown size';
    if (bytes < 1024 * 1024) {
      return `${(bytes / 1024).toFixed(1)} KB`;
    }
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getStatusConfig = (status) => {
    switch (status) {
      case 'uploaded':
        return { colorScheme: 'green', text: 'Uploaded' };
      case 'pending':
        return { colorScheme: 'yellow', text: 'Pending' };
      case 'failed':
        return { colorScheme: 'red', text: 'Failed' };
      default:
        return { colorScheme: 'gray', text: 'Unknown' };
    }
  };

  const statusConfig = getStatusConfig(document.status);

  const getDocumentIcon = () => {
    if (isPNG || isJPEG || isImage) {
      return <Icon as={PDFIcon} boxSize={8} color="green.500" />;
    }
    if (isPDF) {
      return <Icon as={PDFIcon} boxSize={8} color="red.500" />;
    }
    return <Icon as={PDFIcon} boxSize={8} color="gray.500" />;
  };

  const getIconBackground = () => {
    if (isPNG) return 'green.50';
    if (isJPEG) return 'blue.50';
    if (isImage) return 'blue.50';
    if (isPDF) return 'red.50';
    return 'gray.50';
  };

  return (
    <Box
      p={3}
      borderRadius="lg"
      border="1px"
      borderColor="gray.200"
      bg="white"
      shadow="sm"
      _hover={{
        borderColor: colors.primary[300],
        shadow: 'md',
        transform: 'translateY(-1px)'
      }}
      transition="all 0.2s ease-in-out"
      cursor="pointer"
      onClick={() => onPreview(document)}
      position="relative"
      {...cardProps}
    >
      <HStack
        spacing={3}
        align="flex-start"
        position="relative"
        display="flex"
        justifyContent="center"
        alignItems="center"
      >
        {/* Left Side - Document Icon Container */}
        <Box flexShrink={0}>
          <Box
            boxSize="60px"
            borderRadius="md"
            border="1px"
            borderColor="gray.200"
            bg={getIconBackground()}
            display="flex"
            alignItems="center"
            justifyContent="center"
            transition="all 0.2s"
            _hover={{
              borderColor: colors.primary[200],
              transform: 'scale(1.02)'
            }}
          >
            {getDocumentIcon()}
          </Box>
        </Box>

        {/* Right Side - Document Details */}
        <VStack spacing={1} align="flex-start" flex="1" minW={0}>
          <Text
            fontSize="sm"
            fontWeight="semibold"
            color="gray.800"
            noOfLines={2}
            title={t(document.labelKey) || document.label || 'Document'}
            lineHeight="1.3"
            w="full"
          >
            {t(document.labelKey) || document.label || 'Document'}
          </Text>

          <Text
            fontSize="xs"
            color="gray.600"
            noOfLines={1}
            title={document.name}
            fontWeight="medium"
            w="full"
          >
            {document.name}
          </Text>

          <HStack spacing={2} align="center" w="full">
            <Text fontSize="xs" color="gray.500" fontWeight="medium">
              {formatFileSize(document.size)}
            </Text>

            <Badge
              size="sm"
              colorScheme={statusConfig.colorScheme}
              variant="subtle"
              borderRadius="full"
              px={2}
              py={0.5}
              fontSize="10px"
              fontWeight="semibold"
              textTransform="capitalize"
            >
              {statusConfig.text}
            </Badge>
          </HStack>
        </VStack>

        {/* Preview Button - Top Right Corner */}
        <IconButton
          icon={<ExpandIcon />}
          size="lg"
          variant="solid"
          bg={colors.primary[50]}
          borderRadius="full"
          onClick={(e) => {
            e.stopPropagation();
            onPreview(document);
          }}
          _hover={{
            transform: 'scale(1.1)'
          }}
          transition="all 0.2s"
          boxShadow="md"
          zIndex={2}
        />
      </HStack>
    </Box>
  );
};

const DocumentPreviewSection = ({
  documents = [],
  onPreview,
  gridColumns = {
    base: 'repeat(1, 1fr)',
    md: 'repeat(2, 1fr)',
    lg: 'repeat(2, 1fr)',
    xl: 'repeat(3, 1fr)'
  },
  gap = { base: 3, md: 4 },
  emptyStateMessage = 'noDocumentsFound',
  showModal = true,
  ...containerProps
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedDocument, setSelectedDocument] = useState(null);

  const handlePreview = (document) => {
    setSelectedDocument(document);
    if (onPreview) {
      onPreview(document);
    }
    if (showModal) {
      onOpen();
    }
  };

  const hasDocuments = documents && documents.length > 0;

  return (
    <Box
      px={{ base: 4, md: 6 }}
      py={{ base: 3, md: 4 }}
      bg="white"
      {...containerProps}
    >
      {hasDocuments ? (
        <Grid
          templateColumns={gridColumns}
          gap={gap}
        >
          {documents.map((document, index) => (
            <GridItem key={document.id || document.key || index}>
              <DocumentCard
                document={document}
                onPreview={handlePreview}
              />
            </GridItem>
          ))}
        </Grid>
      ) : (
        <Box
          textAlign="center"
          py={12}
          color="gray.500"
        >
          <VStack spacing={3}>
            <Icon as={PDFIcon} boxSize={12} color="gray.300" />
            <Text fontSize="md" fontWeight="medium">
              {t(emptyStateMessage)}
            </Text>
            <Text fontSize="sm" color="gray.400">
              {t('documentsWillAppearHere')}
            </Text>
          </VStack>
        </Box>
      )}

      {/* Document Preview Modal */}
      {showModal && selectedDocument && (
        <DocumentModal
          isOpen={isOpen}
          onClose={onClose}
          fileData={selectedDocument}
        />
      )}
    </Box>
  );
};

export default DocumentPreviewSection;
