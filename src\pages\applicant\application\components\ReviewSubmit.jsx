import React, { useState } from 'react';
import {
  Box,
  Text,
  VStack,
  DocumentPreviewSection
} from 'common/components';
import {
  AccordionComponent, PreviewSection, StepperButtons, Alert as CustomAlert
} from 'common/custom-components';
import { t } from 'i18next';
import { useSelector } from 'react-redux';
import { getScholarshipType } from 'pages/common/selectors';
import colors from 'theme/foundations/colors';
import { STEPPER_STEPS } from '../constants';
import { useSubmitApplicationMutation } from '../api';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

// Dummy data for document preview - labels will be translated in component
const DUMMY_DOCUMENTS = [
  {
    id: 1,
    name: 'aadhaar_card.pdf',
    type: 'application/pdf',
    size: 1024000, // 1MB
    url: '/dummy/aadhaar_card.pdf',
    labelKey: 'aadhaarCard',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    name: 'passport_photo.jpg',
    type: 'image/jpeg',
    size: 512000, // 512KB
    url: '/dummy/passport_photo.jpg',
    labelKey: 'passportPhoto',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:32:00Z'
  },
  {
    id: 3,
    name: 'sslc_marksheet.pdf',
    type: 'application/pdf',
    size: 1536000, // 1.5MB
    url: '/dummy/sslc_marksheet.pdf',
    labelKey: 'sslcMarksheet',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:35:00Z'
  },
  {
    id: 4,
    name: 'income_certificate.pdf',
    type: 'application/pdf',
    size: 768000, // 768KB
    url: '/dummy/income_certificate.pdf',
    labelKey: 'incomeCertificate',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:38:00Z'
  },
  {
    id: 5,
    name: 'bank_passbook.jpg',
    type: 'image/jpeg',
    size: 896000, // 896KB
    url: '/dummy/bank_passbook.jpg',
    labelKey: 'bankPassbook',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:40:00Z'
  },
  {
    id: 6,
    name: 'caste_certificate.pdf',
    type: 'application/pdf',
    size: 1200000, // 1.2MB
    url: '/dummy/caste_certificate.pdf',
    labelKey: 'casteCertificate',
    status: 'uploaded',
    uploadedAt: '2024-01-15T10:42:00Z'
  }
];

const ReviewSubmit = ({
  onPrevious, onEdit, applicationDetails, isDetailsSuccess, applicationId
}) => {
  const scholarshipType = useSelector(getScholarshipType);

  const [submitApplication, { isLoading: isSubmitting }] = useSubmitApplicationMutation();

  const [showAlert, setShowAlert] = useState(false);

  const getScholarshipTypeName = () => {
    if (scholarshipType) {
      return t(scholarshipType);
    }
    return null;
  };

  const getConfirmationMessage = () => {
    const scholarshipName = getScholarshipTypeName();
    return t('submitApplicationConfirmMessage', { scholarshipType: scholarshipName });
  };

  const getAcademicDetailsData = () => {
    return [
      ...getPreviousAcademicDetailsData(applicationDetails, isDetailsSuccess),
      ...getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess)
    ];
  };

  const handleEdit = (section) => {
    if (onEdit) onEdit(section);
  };

  const handleSubmitClick = () => {
    setShowAlert(true);
  };

  const handleAlertClose = () => {
    setShowAlert(false);
  };

  const handleSubmitConfirm = () => {
    submitApplication({
      applicationId,
      closeAlert: () => setShowAlert(false)
    });
  };

  // Accordion data with preview sections
  const accordionData = [
    {
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      onClick: () => handleEdit(STEPPER_STEPS.APPLICANT_DETAILS),
      isCompleted: false
    },
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      onClick: () => handleEdit(STEPPER_STEPS.PARENT_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      onClick: () => handleEdit(STEPPER_STEPS.BANK_DETAILS),
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      onClick: () => handleEdit(STEPPER_STEPS.ACADEMIC_DETAILS),
      isCompleted: false
    }] : []),
    {
      title: t('documentUpload'),
      content: (
        <DocumentPreviewSection documents={DUMMY_DOCUMENTS} />
      ),
      id: 5,
      onClick: () => handleEdit(STEPPER_STEPS.DOCUMENTS_UPLOAD),
      isCompleted: false
    }
  ];

  return (
    <VStack spacing={4} align="stretch">
      {/* Header */}
      <Box alignContent="center" maxWidth={{ base: 'sm', md: 'md', lg: 'lg' }} mx="auto">
        <Text
          fontSize={{ base: 'sm', md: 'lg' }}
          fontWeight="semibold"
          color="white"
          alignItems="center"
          textAlign="center"
          p={2}
          borderRadius="md"
          bg={colors.primary[500]}
        >
          {t('reviewSubmit')}
        </Text>
      </Box>

      {/* Accordion with Preview Sections */}
      <AccordionComponent
        data={accordionData}
        allowMultiple
        currentIndexes={accordionData.map((_, index) => index)}
        isCollapsible={false}
      />

      {/* Action Buttons */}
      <StepperButtons
        currentStep={5}
        totalSteps={6}
        onNext={handleSubmitClick}
        onPrevious={onPrevious}
        layout="space-between"
      />

      {/* Submit Confirmation Alert */}
      <CustomAlert
        open={showAlert}
        close={handleAlertClose}
        variant="success"
        bodyTitle={t('confirmApplication')}
        message={getConfirmationMessage()}
        forwardActionText={t('confirm')}
        backwardActionText={t('cancel')}
        actionForward={handleSubmitConfirm}
        actionBackward={handleAlertClose}
        actionForwardLoading={isSubmitting}
        closeOnOverlayClick={false}
        closeOnEsc={false}
      />
    </VStack>
  );
};

export default ReviewSubmit;
