import React from 'react';
import {
  <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>lex, Heading, HStack, IconButton, Text, VStack, t
} from 'common/components';
import { BackForward, SuccessIcon } from 'assets/svg';

export default function AccountCreatedPage() {
  const handleGoToLogin = () => {
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      as={Card}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
    >
      <Flex alignItems="center" mb={5} position="relative">
        <HStack mb={{ base: 4, md: 6 }} spacing={3}>
          <IconButton
            icon={<BackForward />}
            variant="ghost"
            size="sm"
            aria-label={t('goBack')}
            display={{ base: 'none', md: 'flex' }}
          />
          <VStack align="start" spacing={0} ml={{ base: 0, md: 0 }}>
            <Heading fontSize={{ base: '18px', md: '22px' }} color="blue.600">
              {t('createAccountTitle')}
            </Heading>
            <Text fontSize={{ base: 'xs', md: 'sm' }} color="gray.600">
              {t('registrationCompletionText')}
            </Text>
          </VStack>
        </HStack>
      </Flex>
      <Box
        bg="white"
        p={{ base: '30px 20px', md: '40px 30px' }}
        textAlign="center"
      >
        <Flex justify="center" mb={8}>
          <SuccessIcon />
        </Flex>
        <Heading
          as="h2"
          fontSize="24px"
          fontWeight="600"
          color="primary.500"
          mb={4}
        >
          {t('accountCreatedTitle')}
        </Heading>
        <Text color="gray.500" fontSize="14px" lineHeight="1.5" mb={8}>
          {t('accountCreatedMessage')}
        </Text>

        <Button
          onClick={handleGoToLogin}
          variant="secondary"
          w="full"
          mt={{ base: 3, md: 12 }}
          type="submit"
        >
          {t('goToLoginButton')}
        </Button>
      </Box>
    </Box>
  );
}
