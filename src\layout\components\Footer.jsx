import { Box, Flex, Text } from 'common/components';

const Footer = () => {
  return (
    <Box
      display="grid"
      gridTemplateColumns={{ base: '1fr', md: '1fr 1fr' }}
      alignItems="center"
      gap={4}
      fontSize="sm"
      // color="primary.700"
      py={4}
      px={{ base: 4, md: 8 }}
    >
      <Flex justify={{ base: 'center', md: 'flex-start' }}>
        <Text>
          Copyright © 2024, Department of General Education, Government of Kerala
        </Text>
      </Flex>

      <Flex
        justify={{ base: 'center', md: 'flex-end' }}
        gap={4}
      >
        <Text
          _hover={{ textDecoration: 'underline' }}
          cursor="pointer"
        >
          Terms & Conditions
        </Text>
        <Text
          _hover={{ textDecoration: 'underline' }}
          cursor="pointer"
        >
          Privacy & Policy
        </Text>
      </Flex>
    </Box>
  );
};

export default Footer;
