import { getAadhaarNameFromOtpState } from 'pages/applicant/application/helpers';
import { createSelector } from 'reselect';

export const selectorWithKey = createSelector(
  [
    (state) => state,
    (_state, key) => key
  ],
  (items, category) => {
    return items[category];
  }
);

export const getScholarshipTypeDisplayName = (code) => {
  const mapping = {
    HSS: 'Higher Secondary (HSS)',
    UG: 'Under Graduation (UG)',
    PG: 'Post Graduation (PG)'
  };
  return mapping[code] || code;
};

/**
 * Check if entered name matches the Aadhaar-verified name
 * @param {string} enteredName - Name entered in the form
 * @param {string} aadhaarNumber - Aadhaar number
 * @param {Object} otpState - OTP verification state from Redux
 * @returns {boolean} - true if names match, false otherwise
 */
export const isNameMatchingAadhaarFromOtp = (enteredName, aadhaarNumber, otpState) => {
  if (!enteredName || !aadhaarNumber || !otpState) return false;

  const normalize = (name) => name.toLowerCase().replace(/\./g, '').replace(/\s+/g, ' ').trim();

  const aadhaarName = getAadhaarNameFromOtpState(aadhaarNumber, otpState);
  if (!aadhaarName) return false;

  return normalize(enteredName) === normalize(aadhaarName);
};
