import { bannerConfig } from '../layout/config/bannerConfig';

/**
 * Resolves which banner should be displayed for the current route
 * @param {string} currentPath - The current route path
 * @returns {Object|null} Banner configuration or null if no banner matches
 */
export const resolveBannersForRoute = (currentPath) => {
  // Filter banners based on route matching
  const matchingBanners = bannerConfig
    .filter((banner) => {
      // Check if any route pattern matches the current path using simple contains matching
      return banner.routes.some((routePattern) => currentPath.includes(routePattern));
    })
    // Sort by priority (lower number = higher priority)
    .sort((a, b) => (a.priority || 999) - (b.priority || 999));

  // Return the first (highest priority) banner or null if none match
  return matchingBanners[0] || null;
};
