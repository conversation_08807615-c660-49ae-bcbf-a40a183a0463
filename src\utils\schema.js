import { yupResolver } from '@hookform/resolvers/yup';
import i18next from 'i18next';
import { useMemo } from 'react';

/**
 * Used for translating Schema validation translation
 * Generates a Yup resolver based on the provided schema function.
 *
 * @param {function} getSchema - A function that returns the schema object.
 * @return {function} - The Yup resolver function.
 */

const useSchema = (getSchema) => {
  const locale = i18next.language;
  const resolver = useMemo(getSchema, [locale]);
  return yupResolver(resolver);
};

export default useSchema;
