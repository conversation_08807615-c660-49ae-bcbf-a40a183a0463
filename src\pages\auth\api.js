import { createApi } from '@reduxjs/toolkit/query/react';
import { API_URL, ROUTE_URL } from 'common';
import { t } from 'i18next';
import { _ } from 'utils/lodash';
import { getBaseQuery, handleAPIRequest } from 'utils/http';
import { actions as commonActions } from 'pages/common/slice';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: getBaseQuery(),
  tagTypes: ['register'],
  endpoints: (builder) => ({
    registerUser: builder.mutation({
      query: (data) => ({
        url: API_URL.LOGIN.REGISTER,
        method: 'POST',
        body: data
      }),
      invalidatesTags: ['personalDetails'],
      onQueryStarted: handleAPIRequest({
        successMessage: t('successMessage', { type: t('applicantDetails') }),
        onSuccess: (result, _arg, dispatch) => {
          const { data: { payload: { otpResponse } = {} } = {} } = result;
          if (!_.isEmpty(otpResponse)) {
            dispatch(commonActions.navigateTo({
              to: `/${ROUTE_URL.BASE_PATH}/${ROUTE_URL.AUTH.BASE}/${ROUTE_URL.AUTH.OTP}`,
              options: {
                state: {
                  id: otpResponse.otp.UUID,
                  mobileNumber: _arg.mobileNumber,
                  emailId: _arg.emailId
                },
                replace: false
              },
              isSameModule: true
            }));
          }
        }
      })
    }),
    verifyOTP: builder.mutation({
      query: ({ id, otp, mobileNumber }) => ({
        url: `${API_URL.LOGIN.VERIFY_OTP}?id=${id}&otp=${otp}&mobileNo=${mobileNumber}`,
        method: 'POST'
      }),
      transformErrorResponse: (response) => {
        return response.data?.errors || response.data || {
          message: 'OTP verification failed'
        };
      }
    }),
    resendOTP: builder.mutation({
      query: (mobileNumber) => ({
        url: API_URL.LOGIN.RESEND_OTP,
        method: 'POST',
        params: {
          mobileNo: mobileNumber
        }
      }),
      transformErrorResponse: (response) => {
        return response.data?.errors || response.data || {
          message: 'Failed to resend OTP'
        };
      }
    }),
    loginUser: builder.mutation({
      query: (credentials) => ({
        url: API_URL.LOGIN.VERIFY_LOGIN,
        method: 'POST',
        body: credentials
      }),
      transformErrorResponse: (response) => {
        return response.data?.errors || response.data || {
          message: 'Login failed'
        };
      }
    }),
    verifyToken: builder.query({
      query: () => API_URL.VERIFY_TOKEN
    }),
    forgotPassword: builder.mutation({
      query: (credentials) => ({
        url: API_URL.FORGOT_PASSWORD,
        method: 'POST',
        body: credentials
      }),
      transformErrorResponse: (response) => {
        return response.data?.errors || response.data || {
          message: 'Failed to send reset link'
        };
      }
    }),
    resetPassword: builder.mutation({
      query: (data) => ({
        url: API_URL.RESET_PASSWORD,
        method: 'POST',
        body: data
      }),
      transformErrorResponse: (response) => {
        return response.data?.errors || response.data || {
          message: 'Password reset failed'
        };
      }
    }),
    logoutUser: builder.mutation({
      queryFn: async () => {
        localStorage.clear();
        window.location.href = '/ui/auth/login';

        return { data: { success: true } };
      }
    })
  })
});

export const {
  useRegisterUserMutation,
  useLoginUserMutation,
  useVerifyTokenQuery,
  useForgotPasswordMutation,
  useResetPasswordMutation,
  useVerifyOTPMutation,
  useResendOTPMutation,
  useLogoutUserMutation
} = authApi;
