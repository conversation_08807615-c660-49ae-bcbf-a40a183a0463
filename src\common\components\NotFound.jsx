import {
  Box, Flex, Heading, Text, ChakraLink
} from 'common/components';
import { ROUTE_URL } from 'common';
import { t } from 'i18next';
import { Footer } from 'layout/components';
import { Link } from 'react-router-dom';

const NotFound = ({ footer }) => {
  return (
    <Flex direction="column" minH="100vh">
      <Box as="main" flexGrow={1} display="flex" flexDirection="column" alignItems="center" justifyContent="center" textAlign="center" px={4}>
        <Heading as="h1" fontSize="6xl" fontWeight="bold" color="red.400">
          404
        </Heading>
        <Heading as="h2" fontSize="2xl" mt={2} color="red.400">
          {t('pageNotFound')}
        </Heading>
        <Text mt={4} color="gray.500">
          {t('pageNotFoundInfo')}
        </Text>
        <ChakraLink
          as={Link}
          to={ROUTE_URL.ROOT + ROUTE_URL.BASE_PATH}
          mt={6}
          px={6}
          py={3}
          color="white"
          borderRadius="lg"
          boxShadow="md"
          transition="all 0.2s"
          bgColor="#60C3CE"
          _hover={{
            textDecoration: 'none',
            transform: 'translateY(-1px)',
            boxShadow: 'lg'
          }}
        >
          {t('goBackHome')}
        </ChakraLink>
      </Box>
      {footer && <Footer />}
    </Flex>
  );
};

export default NotFound;
