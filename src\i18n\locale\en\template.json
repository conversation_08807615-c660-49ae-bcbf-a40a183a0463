{"placeholderIn": "{{placeholder}} {{type}}", "isInvalid": "{{type}} is invalid", "invalidType": "Invalid {{type}}", "mustBeAtLeast": "{{type}} must be at least {{count}} {{unit}}", "mustBe": "{{type}} must be {{count}} {{unit}}", "shouldNotBe": "{{type}} should not be {{unit}}", "shouldNotBeGreaterThan": "{{type}} should not greater than {{count}} length", "valueMustBeInBetween": "{{type}} must be between {{start}} and {{end}} {{unit}}", "isRequired": "{{type}} is required", "mustBeAn": "{{type}} must be an integer", "isExists": "{{type}} already exists", "labelIn": "{{label}} ({{type}})", "concatLabel": "{{label}} {{type}}", "asName": "{{type}} Name", "nameOf": "Name of {{type}}", "fetchSuccessForId": "Details Fetched Successfully for {{type}}", "actionBy": "{{action}} by {{person}}", "requestSubmit": "Your Service Request has been submitted to {{type}}", "notAllowed": "{{type}} are not allowed", "acknowledgementMessage": "Request for {{module}} Application is successfully submitted.", "welcomeBack": "Welcome back, {{name}}!", "fieldRequired": "{{field}} is required", "fieldMinLength": "{{field}} must be at least {{min}} characters", "fieldMaxLength": "{{field}} cannot exceed {{max}} characters", "fieldExactLength": "{{field}} must be exactly {{length}} digits", "fieldValidFormat": "Please enter a valid {{field}}", "fieldInvalidFormat": "{{field}} format is invalid", "fieldFutureDate": "{{field}} cannot be in the future", "fieldMinAge": "You must be at least {{age}} years old", "fieldValidMobile": "Please enter a valid 10-digit mobile number starting with 6-9", "fieldValidPassword": "Password must be at least 8 characters long and include at least one digit and one special character", "fieldValidPercentage": "Percentage must be between {{min}} and {{max}}", "fieldSelectOption": "Please select {{field}}", "fieldMinValue": "{{field}} must be at least {{min}}", "fieldMaxValue": "{{field}} must not exceed {{max}}", "successMessage": "{{type}} saved successfully!", "errorMessage": "Error saving {{type}}. Please try again.", "fieldEnter": "Enter your {{field}}"}