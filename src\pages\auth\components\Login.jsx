import {
  <PERSON>, <PERSON><PERSON>, Card, <PERSON>Stack, Text, VStack
} from 'common/components';
import FormController from 'common/components/FormController';
import { EMAIL, MOBILE } from 'common/regex';
import { useForm } from 'react-hook-form';
import { Link, useNavigate } from 'react-router-dom';
import { actions as commonActions } from 'pages/common/slice'; // Correct import
import { useDispatch } from 'react-redux';
import { useLoginUserMutation } from '../api';

const Login = () => {
  const { control, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      email: '',
      password: ''
    }
  });

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loginUser, { isLoading }] = useLoginUserMutation();

  const onSubmit = async (data) => {
    try {
      const credentials = {
        emailOrMobile: data.email,
        password: data.password
      };
      const response = await loginUser(credentials).unwrap();
      if (response.payload.token) {
        localStorage.setItem('token', response.payload.token);

        // Correct dispatch using commonActions
        dispatch(commonActions.setCustomToast({
          open: true,
          variant: 'success',
          message: 'Login successful',
          title: 'Success'
        }));

        if (response.user) {
          localStorage.setItem('userData', JSON.stringify(response.user));
        }

        navigate('/ui/applicant/dashboard');
      }
    } catch (error) {
      const errorMessage = error.data?.errors?.message || 'Invalid credentials. Please try again.';

      // Correct dispatch using commonActions
      dispatch(commonActions.setCustomToast({
        open: true,
        variant: 'error',
        message: errorMessage,
        title: 'Login Failed'
      }));
    }
  };

  return (
    <Box
      py={{ base: 4, md: '2rem' }}
      px={{ base: 4, md: '5rem' }}
      flexDirection="column"
      justifyContent="center"
      as={Card}
      display={{ base: 'block', md: 'flex' }}
      shadow={{ base: 'none', md: 'xl' }}
      borderRadius={{ base: 'none', md: '30px' }}
    >
      <VStack spacing={8} w="full" as="form" onSubmit={handleSubmit(onSubmit)}>
        <VStack spacing={4} textAlign="center">
          <Text fontSize="3xl" fontWeight="bold" color="gray.700">
            Welcome Back
          </Text>
          <Text fontSize="md" color="gray.500">
            Sign in to access your scholarship portal
          </Text>
        </VStack>

        <VStack spacing={6} w="full" mt={12}>
          <FormController
            type="text"
            name="email"
            control={control}
            label="Email Address / Mobile number"
            placeholder="Email Address"
            rules={{
              required: 'Email or mobile number is required',
              validate: (value) => {
                const isEmail = EMAIL.test(value);
                const isPhone = MOBILE.test(value);
                return isEmail || isPhone || 'Please enter a valid email or 10-digit mobile number';
              }
            }}
            errors={errors}
            inputHeight="50px"
          />

          <FormController
            type="password"
            name="password"
            control={control}
            label="Password"
            placeholder="Password"
            rules={{
              required: 'Password is required',
              minLength: {
                value: 8,
                message: 'Password must be at least 8 characters'
              }
            }}
            errors={errors}
            inputHeight="50px"
          />

          <Button
            type="submit"
            variant="secondary"
            w="full"
            mt={{ base: 3, md: 12 }}
            isLoading={isLoading}
            loadingText="Signing in..."
          >
            Sign In
          </Button>

          <VStack spacing={1} fontSize="md" mt={6}>
            <HStack>
              <Text color="gray.500">If you do not have an account?</Text>
              <Link to="/ui/auth/register" style={{ color: 'primary.A100', textDecoration: 'underline' }}>
                Create Account
              </Link>
            </HStack>
            <Link to="/ui/auth/resetPassword" style={{ color: 'primary.A100', textDecoration: 'underline' }}>
              Forgot Password?
            </Link>
          </VStack>
        </VStack>
      </VStack>
    </Box>
  );
};

export default Login;
