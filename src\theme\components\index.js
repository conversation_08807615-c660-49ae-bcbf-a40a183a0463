import ErrorTextStyle from 'common/custom-components/error-text/ErrorTextStyle';
import FormLabelStyle from 'common/custom-components/form-label/FormLabelStyle';
import TextInputStyle from 'common/custom-components/text-input/TextInputStyle';
import DropdownStyle from 'common/custom-components/select-dropdown/DropdownStyle';
import TextAreaStyle from 'common/custom-components/textarea/TextAreaStyle';
import RadioButtonStyle from 'common/custom-components/radio-button/RadioButtonStyle';
import DatePickerStyle from 'common/custom-components/date-picker/DatePickerStyle';
import PaginationStyle from 'common/custom-components/pagination/PaginationStyles';
import accordion from 'common/custom-components/accordion/accordionStyle';
import alert from 'common/custom-components/alert/CustomAlertStyle';
import checkbox from './checkbox';
import button from './button';

const componentStyles = {
  components: {
    Button: button,
    Checkbox: checkbox.component,
    Radio: RadioButtonStyle.component,
    Accordion: accordion.component
  },

  styles: {
    global: {
      ...ErrorTextStyle.style,
      ...FormLabelStyle.style,
      ...TextInputStyle.style,
      ...DropdownStyle.style,
      ...TextAreaStyle.style,
      ...RadioButtonStyle.style,
      ...DatePickerStyle.style,
      ...PaginationStyle.style,
      ...accordion.style,
      ...alert.style
    }
  }
};

export default componentStyles;
