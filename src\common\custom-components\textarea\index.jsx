import { Textarea } from 'common/components';
import React from 'react';
import ErrorText from '../error-text';
import FormLabel from '../form-label';

const TextArea = (props) => {
  const {
    placeHolder, name, label, error, disabled, readOnly, required, size = 'md', style = { width: '100%', height: '100px', lineHeight: '30px' }, ...rest
  } = props;

  return (
    <div className="text-area__container">
      <FormLabel disabled={disabled || readOnly} label={label} required={required} />
      <Textarea
        placeholder={placeHolder}
        className={error && !disabled ? 'error' : ''}
        variant="unstyled"
        type="text"
        size={size}
        id={name}
        name={name}
        style={style}
        disabled={disabled || false}
        readOnly={readOnly || false}
        {...rest}
      />
      {!disabled && error && <ErrorText error={error} />}
    </div>
  );
};

export default TextArea;
