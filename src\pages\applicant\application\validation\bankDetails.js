import { t } from 'common/components';
import * as yup from 'yup';

export const bankDetailsSchema = yup.object().shape({
  accountHolderName: yup
    .string()
    .required(t('accountHolderNameRequired'))
    .matches(/^[a-zA-Z ]*$/, t('onlyLettersAllowed'))
    .min(3, t('min3Characters'))
    .max(50, t('max50Characters')),
  accountNumber: yup
    .string()
    .required(t('accountNumberRequired'))
    .matches(/^[0-9]+$/, t('onlyNumbersAllowed'))
    .min(9, t('min9Digits'))
    .max(18, t('max18Digits')),
  bankName: yup
    .string()
    .required(t('bankNameRequired')),
  branch: yup
    .string()
    .required(t('branchRequired')),
  ifsc: yup
    .string()
    .required(t('ifscRequired'))
    .matches(/^[A-Za-z]{4}0[A-Za-z0-9]{6}$/, t('invalidIFSCFormat'))
});
