name: Continuous Integration

on:
  pull_request:
    branches:
      - develop

jobs:
  call-ci-job:
    uses: ksmartikm/ksm-devops-shared/.github/workflows/fe-ci.yml@main
    with:
      pr-title: "^NRK-.*: .*"
      sonar-check-enabled: true
    secrets:
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      sonar-host-url: ${{ secrets.SONAR_HOST_URL }}
      sonar-token: ${{ secrets.SONAR_TOKEN }}
