import React from 'react';
import Select, { components } from 'react-select';
import { _ } from 'utils/lodash';
import { Tick } from 'assets/svg';
import ErrorText from '../error-text';
import FormLabel from '../form-label';
import { selectStyle } from './DropdownStyle';

const { Option: ReactSelectOption, SingleValue } = components;

const IconOption = ({ isSelected, label, ...rest }) => {
  return (
    <ReactSelectOption
      {...rest}
      className={isSelected ? 'Norka__option--is-selected' : ''}
    >
      <div style={isSelected ? { opacity: 1 } : { opacity: 0 }}>
        <Tick />
      </div>
      {label}
    </ReactSelectOption>
  );
};
const Dropdown = (props) => {
  const {
    label,
    options,
    error,
    disabled,
    required,
    ellipsis,
    placeholder = null,
    optionKey = 'code',
    locale = 'en',
    value,
    lngOptions = {
      en: 'name',
      ml: 'nameInLocal'
    },
    isMulti = false,
    isDisabled,
    formatSelectedValue,
    leftContent,
    ...rest
  } = props;

  const labelAttribute = lngOptions[locale];

  const selectedValue = isMulti
    ? _.filter(options, (item) => _.includes(value || [], _.get(item, optionKey, null)))
    : _.find(options, { [optionKey]: value });

  const customSingleValue = ({ data, ...restProps }) => {
    const display = formatSelectedValue
      ? formatSelectedValue?.(data)
      : data?.[labelAttribute];

    return <SingleValue {...restProps}>{display}</SingleValue>;
  };

  return (
    <>
      <div className={`dropdown-contain ${leftContent ? 'has-left-icon' : ''}`}>
        <FormLabel
          label={label}
          required={required}
          disabled={disabled || isDisabled}
          ellipsis={ellipsis}
        />
        {leftContent && (
          <div className="custom-left-dropdown-content">{leftContent}</div>
        )}
        <Select
          components={{ Option: IconOption, SingleValue: customSingleValue }}
          classNames={{
            control: () => (!disabled && error ? 'Norka__control--error' : '')
          }}
          classNamePrefix="Norka"
          styles={selectStyle}
          getOptionValue={(option) => option[optionKey]}
          getOptionLabel={(option) => option[labelAttribute]}
          options={options}
          isDisabled={disabled || isDisabled}
          value={
            [undefined, null].includes(selectedValue) ? null : selectedValue
          }
          placeholder={placeholder}
          loadingMessage={() => 'Loading...'}
          isMulti={isMulti}
          menuPosition="fixed"
          menuPortalTarget={document.body}
          {...rest}
        />
      </div>
      {!disabled && error && <ErrorText error={error} />}
    </>
  );
};

export default Dropdown;
