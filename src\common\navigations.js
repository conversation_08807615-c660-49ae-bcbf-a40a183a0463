import {
  Dashboard, NewApplication, MyApplication
} from 'assets/svg';
import { ROUTE_URL } from 'common/routeUrls';

export const NAV_ITEMS = [
  {
    icon: Dashboard,
    label: 'Dashboard',
    path: ROUTE_URL.APPLICANT.BASE.DASHBOARD
  },
  {
    icon: NewApplication,
    label: 'New Application',
    path: ROUTE_URL.APPLICANT.BASE.NEW_APPLICATION,
    relatedPaths: [ROUTE_URL.APPLICANT.BASE.APPLICATION]
  },
  {
    icon: MyApplication,
    label: 'My Application',
    path: ROUTE_URL.APPLICANT.BASE.MY_APPLICATIONS
  }
// TODO: commented for future purpose
  // {
  //   icon: UserEdit,
  //   label: 'Profile',
  //   path: null
  // },
  // {
  //   icon: Setting,
  //   label: 'Settings',
  //   path: null
  // }
];
