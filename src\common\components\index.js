import i18next from 'i18next';
import {
  Box, Flex, Text, Image, Button, Icon, Badge,
  Grid, GridItem, SkeletonCircle, SkeletonText, Select, Input,
  Avatar, Heading, Link as ChakraLink, useToast, CircularProgress, VStack,
  useColorModeValue, HStack, Circle, Tooltip, useDisclosure, useMediaQuery,
  Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon,
  IconButton, Breadcrumb, BreadcrumbItem, BreadcrumbLink, Tag, Textarea,
  Menu, MenuButton, MenuList, MenuItem, Drawer, DrawerOverlay,
  DrawerContent, DrawerCloseButton, DrawerBody, Table, Thead, Tbody,
  Tr, Th, Td, Radio, RadioGroup, Stack, InputGroup,
  InputLeftElement, InputRightElement, Container, CardBody, Card, FormErrorMessage,
  Modal, ModalOverlay, ModalContent, ModalHeader, <PERSON>dal<PERSON>ooter, ModalBody, SimpleGrid,
  Spinner, Portal
} from '@chakra-ui/react';

import Navigator from './Navigator';
import CustomLoader from './CustomLoader';
import CommonToast from './CommonToast';
import NotFound from './NotFound';
import ErrorBoundary from './ErrorBoundary';
import RouteAuthorizer from './RouteAuthorizer';
import TitledCard from './TitledCard';
import IconButtonWithLabel from './IconButtonWithLabel';
import LanguageSelector from './LanguageSelect';
import FormController from './FormController';
import CustomAlert from './CustomAlert';
import DashboardBanner from './DashboardBanner';
import BreadcrumbBanner from './BreadcrumbBanner';
import SectionHeading from './SectionHeading';
import PDFViewer from './PdfViewer';
import NewApplication from './NewApplication';
import MyApplication from './MyApplication';
import DocumentPreview from './DocumentPreview';
import DocumentPreviewSection from './DocumentPreviewSection';

export const { t } = i18next;

export {
  Navigator, NotFound, CustomLoader, CommonToast, ErrorBoundary, RouteAuthorizer,
  Icon, Box, SkeletonCircle, SkeletonText, Select, Grid,
  GridItem, Button, useToast, Badge, Flex, Heading,
  Text, ChakraLink, CircularProgress, Image, VStack, useColorModeValue,
  HStack, Circle, Tooltip, Accordion, AccordionItem, AccordionButton,
  AccordionPanel, AccordionIcon, IconButton, TitledCard, Breadcrumb, BreadcrumbItem,
  BreadcrumbLink, Tag, IconButtonWithLabel, useDisclosure, Input, Avatar,
  InputGroup, InputLeftElement, Menu, MenuButton, MenuList, MenuItem, DocumentPreview,
  LanguageSelector, Drawer, DrawerOverlay, DrawerContent, DrawerCloseButton, DrawerBody, Table,
  Thead, Tbody, Tr, Th, Td, useMediaQuery, Radio, RadioGroup, Stack, InputRightElement,
  Textarea, FormController, CustomAlert, DashboardBanner, BreadcrumbBanner, SectionHeading,
  Container, CardBody, Card, FormErrorMessage, Modal, ModalOverlay, ModalContent, ModalHeader,
  ModalFooter, ModalBody, PDFViewer, NewApplication, MyApplication, SimpleGrid, Spinner, Portal,
  DocumentPreviewSection
};
