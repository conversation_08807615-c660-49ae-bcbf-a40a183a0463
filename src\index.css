/* ===== Scrollbar CSS ===== */
/* Apply custom scrollbar to specific containers only */
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: #4B4E84 transparent;
}

/* Chrome, Edge, Safari */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: #4B4E84 ;
  border-radius: 10px;
  border: none;
}

/* Hide the default scrollbar for the entire page */
body,
html {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow: auto;
  /* ✅ Enable scroll if needed */
}

/* Enable scrolling within specific containers */
.scrollable-hero {
  height: 100vh;
  overflow-y: auto;
}

