import {
  Flex, IconButton, Box, Text, Avatar,
  Badge,
  <PERSON>u,
  Menu<PERSON>utton,
  MenuList,
  MenuItem
} from 'common/components';
import { BellIcon, DownArrow } from 'assets/svg';
import { useLogoutUserMutation } from 'pages/auth/api';

const HeaderActions = () => {
  const [logoutUser] = useLogoutUserMutation();

  const handleLogout = () => {
    logoutUser();
  };

  return (
    <Box>
      {/* Desktop Version - Normal header layout */}
      <Flex align="center" gap={4} display={{ base: 'none', md: 'flex' }}>
        {/* Language Selector - Clean minimal style */}
        {/* TO_DO: Commented for future purpose */}
        {/* <LanguageSelector selected="Eng (US)" data={languages} /> */}

        {/* Notification Bell - Simple clean design */}
        <Box pos="relative">
          <Badge pos="absolute" zIndex={4} right={4} top={1} colorScheme="yellow" variant="solid" borderRadius="50%" />
          <IconButton
            icon={<BellIcon />}
            variant="gray"
            size="md"
            aria-label="Notifications"
            color="gray.600"
            bg="gray.100"
            borderRadius="md"
            mx={4}
          />
        </Box>
        {/* User Profile - Clean layout matching image */}
        <Menu>
          <MenuButton as={Box}>
            <Flex align="center" gap={3} cursor="pointer" _hover={{ bg: 'gray.50' }} p={2} borderRadius="md">
              <Avatar
                size="sm"
                name="Aleena"
                src="/path-to-user-image.jpg"
                bg="primary.500"
                color="white"
              />
              <Box>
                <Text fontSize="sm" fontWeight="600" color="gray.800">
                  Aleena
                </Text>
                <Text fontSize="xs" color="gray.500">
                  Student
                </Text>
              </Box>
              {/* Dropdown arrow for user menu */}
              <IconButton
                icon={<DownArrow />}
                variant="ghost"
                size="sm"
                aria-label="User menu"
                color="gray.600"
                _hover={{ bg: 'gray.100' }}
                borderRadius="md"
                minW="auto"
                h="auto"
                p={1}
              />
            </Flex>
          </MenuButton>
          <MenuList>
            <MenuItem onClick={handleLogout}>
              Logout
            </MenuItem>
          </MenuList>
        </Menu>
      </Flex>

      {/* Mobile Version - Fixed bottom navigation */}
      <Box
        display={{ base: 'block', md: 'none' }}
        position="fixed"
        bottom="0"
        left="0"
        right="0"
        bg="white"
        borderTop="1px solid"
        borderColor="gray.200"
        px={4}
        py={3}
        zIndex={1000}
        boxShadow="0 -2px 10px rgba(0, 0, 0, 0.1)"
      >
        <Flex align="center" justify="space-between">
          {/* Language Selector */}
          {/* TO_DO: Commented for future purpose */}
          {/* <LanguageSelector selected="Eng (US)" data={languages} /> */}

          {/* Notification Bell */}
          <IconButton
            icon={<BellIcon />}
            variant="ghost"
            size="md"
            aria-label="Notifications"
            color="gray.600"
            _hover={{ bg: 'gray.100' }}
            borderRadius="md"
          />

          {/* User Profile */}
          <Menu>
            <MenuButton as={Box}>
              <Flex align="center" gap={2} cursor="pointer" _hover={{ bg: 'gray.50' }} p={1} borderRadius="md">
                <Avatar
                  size="sm"
                  name="Aleena"
                  src="/path-to-user-image.jpg"
                  bg="primary.500"
                  color="white"
                />
                <Box>
                  <Text fontSize="sm" fontWeight="600" color="gray.800">
                    Aleena
                  </Text>
                  <Text fontSize="xs" color="gray.500">
                    Student
                  </Text>
                </Box>
                <IconButton
                  icon={<DownArrow />}
                  variant="ghost"
                  size="sm"
                  aria-label="User menu"
                  color="gray.600"
                  _hover={{ bg: 'gray.100' }}
                  borderRadius="md"
                  minW="auto"
                  h="auto"
                  p={1}
                />
              </Flex>
            </MenuButton>
            <MenuList>
              <MenuItem onClick={handleLogout}>
                Logout
              </MenuItem>
            </MenuList>
          </Menu>
        </Flex>
      </Box>
    </Box>
  );
};

export default HeaderActions;
