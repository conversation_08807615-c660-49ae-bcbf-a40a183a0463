import {
  Accordion, AccordionItem, AccordionButton, AccordionPanel, AccordionIcon
} from 'common/components';
import React, { useState, useEffect } from 'react';
import colors from 'theme/foundations/colors';
import { EditIcon } from 'assets/svg';
import InfoMessage from '../InfoMessage';

const handleScroll = (e, top) => {
  window.scrollTo({
    top: Number(e) * 60 + top,
    behavior: 'smooth'
  });
};

const CustomAccordionItem = ({
  title,
  content,
  onClick: onEditClick,
  info: infoMessage,
  id,
  index,
  isCompleted,
  onPress = (data) => data,
  hidden = false,
  isCollapsible = true
}) => {
  return (
    <AccordionItem position="relative" display={hidden ? 'none' : 'block'}>
      <div className="accordionHead">
        <AccordionButton
          className={`accordionButton ${!isCollapsible ? 'non-collapsible' : ''}`}
          onClick={isCollapsible ? () => onPress({ id, title, index }) : undefined}
          _hover={isCollapsible ? { bg: '!primary.50' } : {}}
          cursor={isCollapsible ? 'pointer' : 'default'}
        >
          <h2 className="accordionHeadContent">{`${index + 1}. ${title}`}</h2>

          {(onEditClick || infoMessage || isCompleted) && (
          <div className={`accordionHeaderRightContent ${!isCollapsible ? 'no-collapse-icon' : ''}`}>
            {onEditClick && (
            <EditIcon
              onClick={() => onEditClick({ id, title, index })}
              style={{ cursor: 'pointer' }}
              className="edit-icon-clickable"
            />
            )}
            {infoMessage && <InfoMessage info={infoMessage} />}
            {isCompleted && (
            <svg width="20" height="20" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect y="0.5" width="18" height="18" rx="9" fill="#09893C" />
              <path d="M5 9.36957L7.74286 12.5L13 6.5" stroke="white" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            )}
          </div>
          )}
          {isCollapsible && (
            <div className="accordionIcon">
              <AccordionIcon color={colors?.secondary[50]} />
            </div>
          )}
        </AccordionButton>
      </div>
      <AccordionPanel className="customAccordionPanel">{content}</AccordionPanel>
    </AccordionItem>
  );
};

const AccordionComponent = (props) => {
  const {
    data = [],
    allowMultiple = true,
    currentIndexes = [0],
    onAccordionPress,
    offset = 0,
    scrollEnabled = true,
    isCollapsible = true
  } = props;

  const [activeIndexes, setActiveIndexes] = useState([0]);

  const onActive = (index) => {
    const isActive = Array.isArray(activeIndexes) && activeIndexes.includes(index);
    return isActive;
  };

  const onActiveIndex = (e) => {
    if (allowMultiple) {
      setActiveIndexes([...e]);
    } else {
      setActiveIndexes([e]);
    }
  };

  const handleAccordionChange = (e) => {
    // If not collapsible, don't handle accordion changes
    if (!isCollapsible) return;

    if (onAccordionPress) {
      onAccordionPress(e);
    } else {
      onActiveIndex(e);
    }
    if (scrollEnabled) {
      setTimeout(() => {
        handleScroll(e, offset);
      }, 100);
    }
  };

  // For non-collapsible mode, show all items expanded
  const finalActiveIndexes = isCollapsible ? activeIndexes : data.map((_, index) => index);
  const accordionIndex = finalActiveIndexes?.length > 0 ? { index: finalActiveIndexes } : {};

  useEffect(() => {
    if (isCollapsible && JSON.stringify(currentIndexes) !== JSON.stringify(activeIndexes)) {
      setActiveIndexes(currentIndexes);
      if (scrollEnabled) {
        if (currentIndexes.length === 1) {
          setTimeout(() => {
            handleScroll(currentIndexes[0], offset);
          }, 100);
        }
      }
    }
  }, [JSON.stringify(currentIndexes), isCollapsible]);

  return (
    <Accordion
      allowMultiple={allowMultiple}
      onChange={isCollapsible ? (e) => handleAccordionChange(e) : undefined}
      defaultIndex={isCollapsible ? activeIndexes : data.map((_, index) => index)}
      {...accordionIndex}
    >
      {data.length > 0
        && data.map((item, i) => (
          <CustomAccordionItem
            {...{ ...item, index: i, isCollapsible }}
            key={`${item?.title}`}
            onActive={() => {
              onActive(i);
            }}
          />
        ))}
    </Accordion>
  );
};

export default AccordionComponent;
