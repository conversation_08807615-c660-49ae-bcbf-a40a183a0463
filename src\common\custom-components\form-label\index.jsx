import React, { useState } from 'react';
import { Tooltip, Text, useMediaQuery } from 'common/components';
import { OverflowIcon } from 'assets/svg';

const FormLabel = ({
  label, disabled, required, ellipsis, ...rest
}) => {
  const [isMobile] = useMediaQuery('(max-width: 768px)');
  const [isOpen, setIsOpen] = useState(false);
  const handleClick = () => {
    if (isMobile) {
      setIsOpen(!isOpen);
      setTimeout(() => setIsOpen(false), 2000);
    }
  };
  return (
    <Text
      {...rest}
      className={`form-label ${disabled === true && 'form-label__disabled'} ${ellipsis === true && 'form-label__ellipsis'
      }`}
    >
      {ellipsis && label?.length > 30 && (
        <Tooltip
          placement="top-end"
          initial={{ x: '25vw' }}
          animate={{ x: 0 }}
          hasArrow
          borderRadius="6px"
          label={label}
          bg="primary.500"
          isOpen={isMobile ? isOpen : undefined}
        >
          <div
            className="form-label__overflow"
            onClick={handleClick}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e?.key === 'Enter' || e?.key === ' ') {
                handleClick();
              }
            }}
          >
            <OverflowIcon />
          </div>
        </Tooltip>
      )}
      {label}
      {required && <span> *</span>}
    </Text>
  );
};

export default FormLabel;
