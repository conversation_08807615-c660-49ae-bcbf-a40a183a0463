import React from 'react';
import {
  Box, Table, Thead, Tbody, Tr, Th, Td, <PERSON><PERSON>, Badge, Text, useColorModeValue, Flex,
  Spinner
} from 'common/components';
import { Pagination } from 'common/custom-components';
import { t } from 'i18next';
import { convertToDDMMYYYY } from 'utils/date';

const DynamicTable = ({
  data = [],
  columns = [],
  statusConfig = {},
  onAction = () => {},
  tableProps = {},
  containerProps = {},
  loading = false,
  emptyMessage = 'noDataFound',
  currentPage = 1,
  itemsPerPage = 10,
  totalItems = 0,
  onPageChange = () => {},
  showPagination = true
}) => {
  const headerBg = useColorModeValue('white', 'gray.700');
  const tableBg = useColorModeValue('white', 'gray.800');
  const hoverBg = useColorModeValue('gray.100', 'gray.900');

  const getStatusColor = (status) => {
    return statusConfig[status]?.color || 'gray';
  };

  const getStatusVariant = (status) => {
    return statusConfig[status]?.variant || 'subtle';
  };

  // Render loading state
  const renderLoadingState = () => (
    <Tr>
      <Td colSpan={columns.length} py={12} textAlign="center">
        <Flex direction="column" align="center" gap={3}>
          <Spinner size="lg" color="primary.500" />
        </Flex>
      </Td>
    </Tr>
  );

  // Render empty state
  const renderEmptyState = () => (
    <Tr>
      <Td colSpan={columns.length} py={12} textAlign="center">
        <Text fontSize="md" color="gray.500">
          {t(emptyMessage)}
        </Text>
      </Td>
    </Tr>
  );

  const renderCellContent = (row, column) => {
    const value = row[column.key];

    switch (column.type) {
      case 'status':
        return (
          <Badge
            colorScheme={getStatusColor(value)}
            variant={getStatusVariant(value)}
            px={8}
            py={1}
            borderRadius="full"
            fontSize="xs"
            fontWeight="600"
          >
            {value}
          </Badge>
        );

      case 'action': {
        const actions = typeof column.actions === 'function'
          ? column.actions(row)
          : column.actions;
        return actions?.map((action, index) => (
          <Button
            key={`${index + 2}`}
            size={action.size || 'sm'}
            colorScheme={action.colorScheme || 'blue'}
            variant={action.variant || 'outline'}
            borderRadius={action.borderRadius || 'full'}
            px={action.px || 4}
            py={1}
            fontSize={action.fontSize || 'xs'}
            fontWeight={action.fontWeight || '500'}
            h="32px"
            minW="80px"
            mr={index < actions.length - 1 ? 2 : 0}
            onClick={() => onAction(action.type, row, action)}
          >
            {action.label}
          </Button>
        ));
      }

      case 'date':
        return (
          <Text
            fontSize="sm"
            color={column.color || 'gray.700'}
            fontWeight={column.fontWeight || '500'}
            lineHeight="1.4"
          >
            {value ? convertToDDMMYYYY(value) : ''}
          </Text>
        );

      case 'custom':
        return (
          <Text
            fontSize="sm"
            color={column.color || 'gray.700'}
            fontWeight={column.fontWeight || '500'}
            lineHeight="1.4"
          >
            {column.render ? column.render(value, row) : value}
          </Text>
        );

      case 'text':
      default:
        return (
          <Text
            fontSize="sm"
            color={column.color || 'gray.700'}
            fontWeight={column.fontWeight || '500'}
            lineHeight="1.4"
          >
            {value}
          </Text>
        );
    }
  };

  const getPaginatedData = () => {
    if (totalItems > 0 && totalItems !== data.length) {
      return data;
    }

    if (!showPagination) {
      return data;
    }

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return data.slice(startIndex, endIndex);
  };

  const paginatedData = getPaginatedData();

  return (
    <>
      <Box
        bg={tableBg}
        borderRadius="lg"
        overflow="hidden"
        border="none"
        {...containerProps}
      >
        <Table variant="simple" size="md" {...tableProps}>
          <Thead bg={headerBg}>
            <Tr>
              {columns.map((column, index) => (
                <Th
                  key={`${index + 2}`}
                  color="gray.500"
                  fontWeight="600"
                  fontSize="sm"
                  py={4}
                  px={6}
                  textTransform="none"
                  letterSpacing="wider"
                  {...column.headerProps}
                >
                  {column.header}
                </Th>
              ))}
            </Tr>
          </Thead>
          <Tbody>
            {(() => {
              if (loading) {
                return renderLoadingState();
              }
              if (paginatedData.length === 0) {
                return renderEmptyState();
              }
              return paginatedData.map((row, rowIndex) => (
                <Tr
                  key={`${rowIndex + 2}`}
                  bg={rowIndex % 2 === 1 ? 'primary.50' : 'white'}
                  _hover={{ bg: hoverBg }}
                  {...row.rowProps}
                >
                  {columns.map((column, colIndex) => (
                    <Td
                      key={`${colIndex + 2}`}
                      py={2}
                      px={6}
                      borderBottom="none"
                      {...column.cellProps}
                    >
                      {renderCellContent(row, column)}
                    </Td>
                  ))}
                </Tr>
              ));
            })()}
          </Tbody>
        </Table>

      </Box>
      {/* Pagination Footer */}
      {showPagination && totalItems > 0 && !loading && (
      <Box display="flex" justifyContent="flex-end" alignItems="center">
        <Pagination
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          totalItems={totalItems}
          handlePageChange={onPageChange}
        />
      </Box>
      )}
    </>
  );
};

export default DynamicTable;
