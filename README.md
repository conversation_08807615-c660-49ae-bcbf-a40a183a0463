# Norka Roots [![Commitizen friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](http://commitizen.github.io/cz-cli/)

## Getting Started with Vite

This project was bootstrapped with [Vite]().
### Node version
Use `v20` or above.

### Package Manager
pnpm
[https://pnpm.io/installation] (installation) 

## Available Scripts

In the project directory, you can run:
### `pnpm dev`

Runs the app in the development mode.\
Open [http://localhost:5002](http://localhost:5002) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

###### Dependencies and their references

1. https://vitejs.dev/guide/
2. https://v2.chakra-ui.com/
3. https://eslint.org/docs/latest/use/getting-started
4. https://www.npmjs.com/package/husky
5. https://redux-toolkit.js.org/rtk-query/overview
