import colors from 'theme/foundations/colors';

const commonButtonStyle = {
  bg: 'secondary.700',
  color: 'white',
  borderRadius: '4px',
  padding: '12px 40px',
  height: 'max-content',
  border: `1px solid ${colors.tertiary[500]}`,
  boxShadow: `0px 9px 21px -14px ${colors.gray[300]}`
};

export default {
  baseStyle: {
    fontSize: '16px'
  },
  variants: {
    secondary: {
      ...commonButtonStyle,
      border: `1px solid ${colors.secondary[500]}`,
      _hover: {
        bg: 'secondary.400'
      },
      _active: {
        bg: 'secondary.400'
      }
    },
    secondary_outline: {
      ...commonButtonStyle,
      bg: 'white',
      color: 'secondary.500',
      border: `1px solid ${colors.secondary[500]}`,
      _hover: {
        bg: 'secondary.50',
        color: 'secondary.700'
      },
      _active: {
        bg: 'secondary.100',
        color: 'secondary.800'
      }
    },
    secondary_light: {
      ...commonButtonStyle,
      bg: 'secondary.50',
      color: 'secondary.700',
      border: 'none',
      boxShadow: 'none',
      _hover: {
        bg: 'secondary.100',
        color: 'secondary.800'
      },
      _active: {
        bg: 'secondary.200',
        color: 'secondary.900'
      }
    },
    primary: {
      ...commonButtonStyle,
      bg: 'primary.500',
      border: `1px solid ${colors.primary[300]}`,
      _hover: {
        bg: 'primary.400',
        borderColor: colors.primary[400]
      },
      _active: {
        bg: 'primary.600',
        borderColor: colors.primary[500]
      }
    },
    primary_outline: {
      ...commonButtonStyle,
      bg: 'white',
      color: 'primary.700',
      _hover: {
        bg: 'primary.50',
        color: 'primary.700'
      },
      _active: {
        bg: 'primary.100',
        color: 'primary.800'
      },
      border: `1px solid ${colors.primary[300]}`
    },
    primary_light: {
      ...commonButtonStyle,
      bg: 'primary.80',
      color: 'primary.700',
      border: 'none',
      boxShadow: 'none',
      _hover: {
        bg: 'primary.100',
        color: 'primary.800'
      },
      _active: {
        bg: 'primary.200',
        color: 'primary.900'
      }
    }

  }
};
