import React from 'react';
import {
  Box,
  Grid,
  Grid<PERSON><PERSON>,
  FormController,
  TitledCard
} from 'common/components';
import { t } from 'i18next';
import { YES_NO_OPTIONS } from 'pages/common/constant';
import { FormLabel } from 'common/custom-components';
import { FAMILY_CIRCUMSTANCES_OPTIONS } from '../constants';

const AdditionalDetails = ({ control, errors }) => {
  return (
    <TitledCard title={t('additionalDetails')}>
      <Box p={{ base: 4, md: 6 }}>
        <Grid templateColumns="repeat(1, 1fr)" gap={{ base: 4, md: 6, lg: 8 }}>
          {/* Family Circumstances - Consolidated */}
          <GridItem>
            <FormLabel label={t('familyCircumstances')} />
            <FormController
              type="radio"
              label={t('familyCircumstances')}
              name="familyCircumstances"
              control={control}
              errors={errors}
              options={FAMILY_CIRCUMSTANCES_OPTIONS}
              optionKey="code"
              direction="column"
              radioGap={4}
            />
          </GridItem>

          {/* Have you represented at the State Level in Sports/Arts? - Separate */}
          <GridItem>
            <FormLabel label={t('hasRepresentedAtStateLevel')} />
            <FormController
              type="radio"
              label={t('hasRepresentedAtStateLevel')}
              name="hasRepresentedAtStateLevel"
              control={control}
              errors={errors}
              options={YES_NO_OPTIONS}
              optionKey="code"
              direction="row"
            />
          </GridItem>
        </Grid>
      </Box>
    </TitledCard>
  );
};

export default AdditionalDetails;
