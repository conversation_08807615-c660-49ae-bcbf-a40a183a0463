import React, { useEffect, useState } from 'react';
import {
  Radio, RadioGroup, Stack, Text
} from 'common/components';
import { _ } from 'utils/lodash';
import FormLabel from '../form-label';

const RadioButton = (props) => {
  const {
    radioGap = 5,
    name,
    label,
    error,
    options,
    disabled,
    onChange = () => { },
    variant = 'normal',
    value,
    disabledIndex = [],
    optionKey = 'name',
    required,
    ellipsis,
    readOnly,
    direction = 'row'
  } = props;

  const selectedValue = _.find(options, { [optionKey]: value }) || {};

  const [border, setBorder] = useState('');

  const handleInput = (style) => {
    if (error && !disabled) {
      setBorder('error');
    } else {
      setBorder(style);
    }
  };

  useEffect(() => {
    if (disabled) {
      setBorder('');
    }
  }, []);

  useEffect(() => {
    handleInput('');
  }, [error]);

  return (
    <div
      className={
        variant === 'tabed'
          ? 'input__container tabed-radio'
          : `input__container ${variant}-radio`
      }
    >
      <fieldset
        className={border}
        onFocusCapture={() => handleInput('active')}
        onBlur={() => handleInput('')}
      >
        {variant === 'outlined' && (
          <legend>
            <FormLabel
              disabled={disabled || readOnly}
              label={label}
              required={required}
              ellipsis={ellipsis}
            />
          </legend>
        )}
        <RadioGroup
          isDisabled={readOnly || disabled}
          name={name}
          className={variant}
          onChange={(data) => onChange(data)}
          value={selectedValue[optionKey]}
        >
          <Stack
            direction={direction}
            justifyContent="start"
            flexWrap={{ base: 'wrap', sm: 'nowrap' }}
            gap={radioGap}
          >
            {options?.map((item, index) => {
              return (
                <Radio
                  key={item[optionKey]}
                  value={item[optionKey]}
                  colorScheme="secondary"
                  isDisabled={disabled || disabledIndex.includes(index)}
                >
                  {item.name}
                </Radio>
              );
            })}
          </Stack>
        </RadioGroup>
      </fieldset>
      {!disabled && error && <Text className="radio-error">{error}</Text>}
    </div>
  );
};

export default RadioButton;
