/* eslint-disable import/no-extraneous-dependencies */
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import jsconfigPaths from 'vite-jsconfig-paths';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  // Fallback to '/' if VITE_BASE_URL is not defined
  const baseUrl = env.VITE_BASE_URL || '/';

  return {
    base: baseUrl,
    experimental: {
      renderBuiltUrl: (filename) => `${baseUrl}${filename}`
    },
    plugins: [react(), jsconfigPaths()],
    optimizeDeps: {
      include: [
        '@chakra-ui/react',
        '@emotion/react',
        '@emotion/styled',
        'framer-motion'
      ]
    }
  };
});
