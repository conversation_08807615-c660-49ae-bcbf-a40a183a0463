import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.center': {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '12px',
    paddingTop: '20px !important',
    gap: '8px'
  },

  '.mr-5': {
    marginRight: '5px'
  },

  '.indexBtn, .prevBtn, .NxtBtn': {
    borderRadius: '4px',
    padding: '8px 14px',
    fontWeight: '500',
    fontSize: '15px',
    color: colors.gray[600],
    backgroundColor: 'transparent',
    border: 'none',
    cursor: 'pointer',
    transition: 'background 0.4s ease-in-out',
    minWidth: '40px',
    minHeight: '40px',
    _hover: {
      background: colors.gray[100]
    }
  },

  '.activeIndexBtn': {
    borderRadius: '4px',
    padding: '8px 14px',
    fontWeight: '600',
    fontSize: '15px',
    color: colors.gray[800],
    backgroundColor: colors.primary[80],
    border: 'none',
    minWidth: '40px',
    minHeight: '40px'
  },

  '.activePrevBtn, .activeNxtBtn': {
    borderRadius: '4px',
    padding: '8px 14px',
    fontWeight: '500',
    fontSize: '15px',
    opacity: 0.4,
    cursor: 'not-allowed',
    border: 'none',
    minWidth: '40px',
    minHeight: '40px'
  },

  '.pagination-dots': {
    fontSize: '15px',
    color: colors.gray[400],
    padding: '8px 10px',
    minHeight: '40px',
    display: 'flex',
    alignItems: 'center'
  }
};

export default { component, style };
