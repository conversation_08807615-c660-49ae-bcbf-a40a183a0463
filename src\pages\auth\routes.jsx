import { ROUTE_URL } from 'common';
import { lazy } from 'react';

const AuthPage = lazy(() => import('./components'));

const routes = [
  {
    path: ROUTE_URL.AUTH.LOGIN,
    element: <AuthPage />
  },
  {
    path: ROUTE_URL.AUTH.REGISTER,
    element: <AuthPage />
  },
  {
    path: ROUTE_URL.AUTH.OTP,
    element: <AuthPage />
  },
  {
    path: ROUTE_URL.AUTH.SUCCESS,
    element: <AuthPage />
  },
  {
    path: ROUTE_URL.AUTH.RESETPASSWORD,
    element: <AuthPage />
  },
  {
    path: ROUTE_URL.AUTH.CHANGEPASSWORD,
    element: <AuthPage />
  }
];

export { routes };
