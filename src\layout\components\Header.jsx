import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, RPLogo } from 'assets/images';
import { Hamburger } from 'assets/svg';
import {
  Box, Flex, IconButton, Image
} from 'common/components';
import HeaderActions from './HeaderActions';

function Header({ onSidebarToggle, sidebarWidth = '250px', isSidebarExpanded = true }) {
  return (
    <Box
      as="header"
      bg="white"
      color="primary.500"
      px={{ base: 4, md: 6 }}
      py={2}
      position="fixed"
      top="0"
      left={{ base: '0', md: isSidebarExpanded ? sidebarWidth : '0' }}
      transition="left 0.3s ease-in-out"
      right="0"
      zIndex={10}
      boxShadow="0 1px 3px -2px rgba(0, 0, 0, 0.1)"
      borderBottom="1px solid"
      borderColor="gray.200"
    >
      <Flex align="center" justifyContent="space-between" wrap="wrap" h="48px">
        {/* Left Section - Mobile Logo and Hamburger */}
        <Flex gap={3} w={{ base: '100%', md: 'auto' }} justifyContent="space-between" align="center">
          {/* RP Logo - Only show on mobile */}
          <Box display={{ base: 'flex', md: 'none' }} align="center" ml={2}>
            <Image
              src={RPLogo}
              width="110px"
              alt="RP Logo"
              objectFit="contain"
            />
          </Box>

          {/* Sidebar Logos - Only show on desktop when sidebar is shrunk */}
          {!isSidebarExpanded && (
            <Flex gap={3} align="center" display={{ base: 'none', md: 'flex' }} ml={2}>
              <Image
                src={KeralaLogo}
                width="60px"
                height="50px"
                alt="Kerala Logo"
                objectFit="contain"
              />
              <Image
                src={RootsLogo}
                width="90px"
                height="40px"
                alt="Roots Logo"
                objectFit="contain"
              />
            </Flex>
          )}

          {/* Hamburger Menu - Mobile only */}
          <IconButton
            height="48px"
            width="48px"
            icon={<Hamburger height="20px" />}
            variant="ghost"
            size="md"
            aria-label="Toggle menu"
            display={{ base: 'inline-flex', md: 'none' }}
            onClick={onSidebarToggle}
            _hover={{ bg: 'gray.100' }}
          />
        </Flex>

        {/* Center Section - Search Bar */}
        {/* TODO : commented for future purpose */}
        {/* <Box
          flex="1"
          maxW="500px"
          mx={{ base: 4, md: 8 }}
          display={{ base: 'none', md: 'block' }}
        >
          <InputGroup>
            <InputLeftElement pointerEvents="none" h="40px">
              <SearchIcon color="gray.400" />
            </InputLeftElement>
            <Input
              placeholder="Search here..."
              h="40px"
              bg="gray.50"
              border="1px solid"
              borderColor="gray.200"
              borderRadius="8px"
              _focus={{
                bg: 'white',
                borderColor: 'primary.300',
                boxShadow: '0 0 0 1px rgba(2, 87, 154, 0.2)'
              }}
              _placeholder={{ color: 'gray.500' }}
            />
          </InputGroup>
        </Box> */}

        {/* Right Section - User Actions */}
        <Flex align="center" display={{ base: 'none', md: 'flex' }}>
          <HeaderActions />
        </Flex>
      </Flex>
    </Box>
  );
}

export default Header;
