import React, { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { _ } from 'utils/lodash';
import { ChakraProvider } from '@chakra-ui/react';
import {
  CommonToast, CustomLoader, ErrorBoundary, Navigator, RouteAuthorizer
} from 'common/components';
import { routes } from 'pages/routes';
import { store } from 'app/store';
import './index.css';
import './i18n';
import theme from 'theme';

const root = createRoot(document.getElementById('root'));

const renderRoutes = (appRoutes, parentPath = '') => appRoutes?.map((route, index) => {
  const keyIndex = `${index}-route`;
  const fullPath = parentPath
      + (!_.startsWith('/', route.path) && !_.endsWith('/', parentPath) ? '/' : '')
      + route.path;

  return (
    <Route
      key={keyIndex}
      path={route.path}
      index={route?.index || false}
      element={(
        <ErrorBoundary key={fullPath} parentPath={fullPath}>
          <RouteAuthorizer
            path={fullPath}
            element={route.element}
            roles={route.roles || []}
          />
        </ErrorBoundary>
        )}
      {...(route.errorElement && { errorElement: route.errorElement })}
    >
      {route.children && renderRoutes(route.children, fullPath)}
    </Route>
  );
});

root.render(
  <React.StrictMode>
    <Provider store={store}>
      <ChakraProvider theme={theme}>
        <Suspense fallback={<div><CustomLoader /></div>}>
          <BrowserRouter>
            <Routes>{renderRoutes(routes)}</Routes>
            <Navigator />
            <CommonToast />
          </BrowserRouter>
        </Suspense>
      </ChakraProvider>
    </Provider>
  </React.StrictMode>
);
