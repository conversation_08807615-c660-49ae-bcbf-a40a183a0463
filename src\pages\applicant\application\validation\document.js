import { t } from 'common/components';
import * as yup from 'yup';
import { FILE_SIZE, VALID_FILE_TYPE } from 'pages/common/constant';

const fileTypeTest = (label) => yup.mixed()
  .test('fileFormat', t('invalidFileFormat'), (value) => !value || VALID_FILE_TYPE.includes(value.type))
  .test('file-required', t('isRequired', { type: t(label) }), (value) => value instanceof File)
  .test('fileSize', t('fileSizeExceeded'), (value) => !value || value.size <= FILE_SIZE);

export const documentSchema = yup.object().shape({
  aadhaar: yup.mixed()
    .required(t('aadhaar'))
    .concat(fileTypeTest('aadhaar')),

  passport: yup.mixed()
    .required(t('passportPhoto'))
    .concat(fileTypeTest('passport')),

  marksheet: yup.mixed().when('isHSSApplicant', {
    is: true,
    then: yup.mixed()
      .required(t('marksheet'))
      .concat(fileTypeTest('marksheet'))
  }),

  ugMarksheet: yup.mixed().when('isPGApplicant', {
    is: true,
    then: yup.mixed()
      .required(t('ugMarksheet'))
      .concat(fileTypeTest('ugMarksheet'))
  }),

  scoreCard: yup.mixed().when('isPGApplicant', {
    is: true,
    then: yup.mixed()
      .required(t('scoreCard'))
      .concat(fileTypeTest('scoreCard'))
  }),

  incomeCertificate: yup.mixed()
    .required(t('incomeCertificate'))
    .concat(fileTypeTest('incomeCertificate')),

  specialCertificate: yup.mixed().when('hasSpecialStatus', {
    is: true,
    then: yup.mixed()
      .required(t('specialCertificate'))
      .concat(fileTypeTest('specialCertificate'))
  }),

  achievementCertificate: yup.mixed()
    .concat(fileTypeTest('achievementCertificate')),

  bankPassbook: yup.mixed()
    .required(t('bankPassbook'))
    .concat(fileTypeTest('bankPassbook')),

  rationCard: yup.mixed()
    .required(t('rationCard'))
    .concat(fileTypeTest('rationCard'))
}).required();
