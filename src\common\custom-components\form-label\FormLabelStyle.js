import colors from 'theme/foundations/colors';

const component = {};

const style = {
  '.form-label': {
    paddingLeft: '0.2rem',
    paddingRight: '0.2rem',
    fontWeight: '400',
    fontSize: '14px',
    lineHeight: 'normal',
    color: colors.tertiary[500],
    position: 'relative',
    span: {
      fontSize: '14px',
      color: colors.primary[500],
      padding: '0px'
    },
    '&__ellipsis': {
      maxWidth: '212px',
      whiteSpace: 'nowrap',
      overflow: 'hidden'
    },
    '&__overflow': {
      padding: '4px',
      backgroundColor: 'white',
      position: 'absolute',
      left: '168px',
      top: '-5px'
    },
    '&__disabled': {
      color: colors.gray[400]
    }
  }

};

export default { component, style };
