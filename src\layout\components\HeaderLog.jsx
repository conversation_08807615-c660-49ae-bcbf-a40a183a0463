import { Kerala<PERSON>ogo, RootsLogo, RPLogo } from 'assets/images';
import { Box, Image } from 'common/components';
import React from 'react';

const HeaderLog = () => {
  return (
    <div>
      <Box display="flex" mt={2} gap={4} marginLeft={{ base: 0, md: 8 }} w="100%">
        <Image src={RPLogo} width={{ base: '110px', md: '45px' }} alt="Kerala Logo" objectFit="contain" display={{ base: 'inline-flex', md: 'none' }} />
        <Image src={KeralaLogo} ml={100} width={{ base: '60px', md: '75px' }} alt="Kerala Logo" objectFit="contain" />
        <Image src={RootsLogo} width={{ base: '60px', md: '75px' }} alt="Roots Logo" objectFit="contain" />
      </Box>
    </div>
  );
};

export default HeaderLog;
