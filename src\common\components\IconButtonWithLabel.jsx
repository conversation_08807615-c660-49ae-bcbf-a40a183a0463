import { Button, Icon } from 'common/components';
import { forwardRef, isValidElement } from 'react';

const IconButtonWithLabel = forwardRef(
  (
    {
      label,
      icon,
      iconPosition = 'right',
      variant = 'primary',
      colorScheme = 'primary',
      fontWeight = 'medium',
      size = 'md',
      isLoading = false,
      isDisabled = false,
      iconSize = 4,
      iconSpacing = 2,
      px = 6,
      py = 2.5,
      ...rest
    },
    ref
  ) => {
    let iconElement = null;

    if (icon) {
      iconElement = isValidElement(icon)
        ? icon
        : <Icon as={icon} boxSize={iconSize} />;
    }

    return (
      <Button
        ref={ref}
        variant={variant}
        colorScheme={colorScheme}
        fontWeight={fontWeight}
        size={size}
        isLoading={isLoading}
        isDisabled={isDisabled}
        iconSpacing={iconSpacing}
        px={px}
        py={py}
        {...(iconPosition === 'left'
          ? { leftIcon: iconElement }
          : { rightIcon: iconElement })}
        {...rest}
      >
        {label}
      </Button>
    );
  }
);

export default IconButtonWithLabel;
