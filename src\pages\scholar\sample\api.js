import { createApi } from '@reduxjs/toolkit/query/react';
import { API_URL } from 'common';
import { getBaseQuery } from 'utils/http';

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: getBaseQuery(),

  endpoints: (builder) => ({
    registerUser: builder.mutation({
      query: (data) => ({
        url: API_URL.LOGIN.REGISTER,
        method: 'POST',
        body: data
      }),
      transformErrorResponse: (response) => {
        if (response.data?.errorType === 'DUPLICATE_NOKIA_ID') {
          return {
            field: 'nokiaId',
            message: response.data.message || 'This Nokia ID is already registered'
          };
        }
        if (response.data?.errorType === 'DUPLICATE_MOBILE') {
          return {
            field: 'mobileNumber',
            message: response.data.message || 'This mobile number is already registered'
          };
        }
        return {
          message: response.data?.message || 'Registration failed'
        };
      }
    }),

    loginUser: builder.mutation({
      query: (credentials) => ({
        url: API_URL.LOGIN,
        method: 'POST',
        body: credentials
      })
    }),

    verifyToken: builder.query({
      query: () => API_URL.VERIFY_TOKEN
    })
  })
});

export const {
  useRegisterUserMutation,
  useLoginUserMutation,
  useVerifyTokenQuery
} = authApi;
